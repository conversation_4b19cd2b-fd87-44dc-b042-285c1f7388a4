# GetX Router to go_router Migration Guide

This guide provides a step-by-step process for gradually migrating screens from GetX router to go_router while maintaining full backward compatibility.

## Overview

Our hybrid navigation system allows you to migrate screens one at a time, testing each thoroughly before proceeding to the next. The system automatically routes between GetX and go_router based on route configuration.

## Architecture

### Key Components

1. **NavigationService** (`lib/app/services/navigation_service.dart`)
   - Hybrid service that automatically chooses between GetX and go_router
   - Provides unified API for navigation calls

2. **RouteNames** (`lib/app/routes/route_names.dart`)
   - Centralized route management
   - Tracks which routes have been migrated

3. **GoRouterConfig** (`lib/app/routes/go_router_config.dart`)
   - go_router configuration for migrated routes

4. **HybridApp** (`lib/app/widgets/hybrid_app.dart`)
   - App wrapper that handles both routing systems

## Migration Process

### Step 1: Analyze the Current Screen

Before migrating, understand the current screen's navigation patterns:

```dart
// Common GetX navigation patterns to look for:
Get.back()                    // Going back
Get.toNamed('/route')         // Navigate to route
Get.offNamed('/route')        // Replace current route
Get.offAllNamed('/route')     // Clear stack and navigate
Get.dialog()                  // Show dialog
```

### Step 2: Create Hybrid View

Create a new hybrid version of your view:

**File naming convention:** `*_view_hybrid.dart`

```dart
// Example: lib/app/modules/example/views/example_view_hybrid.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../routes/route_names.dart';
import '../../../services/navigation_service.dart';
import '../controllers/example_controller.dart';

class ExampleViewHybrid extends StatefulWidget {
  const ExampleViewHybrid({super.key});

  @override
  State<ExampleViewHybrid> createState() => _ExampleViewHybridState();
}

class _ExampleViewHybridState extends State<ExampleViewHybrid> {
  final ExampleController controller = Get.put(ExampleController());
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    navigationService = NavigationService.instance;
  }

  @override
  void dispose() {
    Get.delete<ExampleController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Copy existing view content here
    // Replace navigation calls with hybrid navigation
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            // Replace: Get.back()
            navigationService.back();
          },
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: Column(
        children: [
          ElevatedButton(
            onPressed: () {
              // Replace: Get.toNamed('/other-route')
              navigationService.toNamed(RouteNames.otherRoute);
            },
            child: const Text('Navigate'),
          ),
        ],
      ),
    );
  }
}
```

### Step 3: Update Route Names

Add your route to the migrated routes list:

```dart
// lib/app/routes/route_names.dart

abstract class RouteNames {
  // Add your new route constant
  static const String example = '/example';
  
  // Add to migrated routes
  static Set<String> get migratedRoutes => {
    login,
    register,
    navigationTest,
    example, // Add here
  };
  
  // Remove from GetX routes if it exists there
  static Set<String> get getxRoutes => {
    // Remove 'example' from here if it exists
  };
}
```

### Step 4: Add Route to go_router Configuration

```dart
// lib/app/routes/go_router_config.dart

import '../modules/example/views/example_view_hybrid.dart';

class GoRouterConfig {
  static GoRouter createRouter() {
    return GoRouter(
      routes: [
        // Add your new route
        GoRoute(
          path: '/example',
          name: 'example',
          builder: (context, state) {
            return const ExampleViewHybrid();
          },
        ),
        // ... other routes
      ],
    );
  }
}
```

### Step 5: Update Navigation Calls

Replace GetX navigation calls with hybrid navigation:

```dart
// Before (GetX)
Get.back()
Get.toNamed('/example')
Get.offNamed('/example')
Get.offAllNamed('/example')

// After (Hybrid)
NavigationService.instance.back()
NavigationService.instance.toNamed(RouteNames.example)
NavigationService.instance.offNamed(RouteNames.example)
NavigationService.instance.offAllNamed(RouteNames.example)
```

### Step 6: Update References

Update any files that reference the old view:

```dart
// Before
import '../../example/views/example_view.dart';
return const ExampleView();

// After
import '../../example/views/example_view_hybrid.dart';
return const ExampleViewHybrid();
```

### Step 7: Test the Migration

1. **Add to navigation test widget:**
```dart
// lib/app/widgets/navigation_test_widget.dart
ElevatedButton(
  onPressed: () {
    navigationService.toNamed(RouteNames.example);
  },
  child: const Text('Test Example (go_router)'),
),
```

2. **Test scenarios:**
   - Navigate to the screen
   - Navigate from the screen to other screens
   - Back navigation
   - Deep linking (if applicable)
   - Arguments passing

## Common Migration Patterns

### 1. Simple Navigation

```dart
// GetX
onPressed: () => Get.toNamed('/profile'),

// Hybrid
onPressed: () => navigationService.toNamed(RouteNames.profile),
```

### 2. Navigation with Arguments

```dart
// GetX
Get.toNamed('/profile', arguments: {'userId': 123})

// Hybrid
navigationService.toNamed(RouteNames.profile, arguments: {'userId': 123})
```

### 3. Back Navigation

```dart
// GetX
Get.back(result: 'success')

// Hybrid
navigationService.back('success')
```

### 4. Replace Current Route

```dart
// GetX
Get.offNamed('/home')

// Hybrid
navigationService.offNamed(RouteNames.home)
```

### 5. Clear Stack and Navigate

```dart
// GetX
Get.offAllNamed('/login')

// Hybrid
navigationService.offAllNamed(RouteNames.login)
```

## Special Cases

### Modal Bottom Sheets

For screens shown as modal bottom sheets, you have two options:

**Option A: Keep as modal (recommended for initial migration)**
```dart
showModalBottomSheet(
  builder: (context) => const ExampleViewHybrid(),
  context: context,
);
```

**Option B: Convert to full-screen navigation**
```dart
navigationService.toNamed(RouteNames.example);
```

### Dialogs

Keep using `Get.dialog()` for now, as dialogs are different from page navigation:

```dart
Get.dialog(
  KMessageDialogView(content: 'Message'),
  barrierDismissible: false,
);
```

### Bindings

For screens that use GetX bindings, initialize them in the route builder:

```dart
GoRoute(
  path: '/example',
  builder: (context, state) {
    // Initialize binding
    ExampleBinding().dependencies();
    return const ExampleViewHybrid();
  },
),
```

## Testing Checklist

- [ ] Screen loads correctly
- [ ] All navigation from the screen works
- [ ] Back navigation works
- [ ] Arguments are passed correctly
- [ ] No compilation errors
- [ ] No runtime errors
- [ ] UI/UX remains unchanged
- [ ] Deep linking works (if applicable)

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure to import the hybrid view, not the original
2. **Route conflicts**: Ensure route is removed from GetX routes and added to migrated routes
3. **Navigation not working**: Check that NavigationService is properly initialized
4. **Arguments not passed**: Use the `arguments` parameter in navigation calls

### Debug Tips

1. Use the navigation test widget to verify routes work
2. Check console logs for navigation errors
3. Use `flutter analyze` to catch compilation issues
4. Test on both debug and release builds

## Best Practices

1. **Migrate simple screens first** - Start with screens that have minimal navigation complexity
2. **Test thoroughly** - Each migrated screen should be fully tested before moving to the next
3. **Keep UI/UX unchanged** - Users should not notice any difference
4. **Maintain backward compatibility** - Don't break existing functionality
5. **Document changes** - Keep track of what has been migrated

## Example Migration: Login Screen

See the successful migration of `LoginView` to `LoginViewHybrid` as a reference:

- **Original**: `lib/app/modules/login/views/login_view.dart`
- **Hybrid**: `lib/app/modules/login/views/login_view_hybrid.dart`
- **Changes**: All `Get.toNamed()`, `Get.back()` calls replaced with `NavigationService`
- **Result**: Seamless navigation with go_router while maintaining existing UI/UX

## Next Steps

After completing a migration:

1. Test the migrated screen thoroughly
2. Update any integration tests
3. Consider migrating related screens
4. Document any issues or learnings
5. Plan the next screen to migrate

## Migration Progress Tracking

Keep track of your migration progress:

```dart
// Current migrated routes (as of this guide):
✅ /login - Login screen
✅ /register - Register screen
✅ /profile-edit - Profile edit screen
✅ /navigation-test - Navigation test widget
✅ /forgot-password-email - Forgot password email screen
✅ /forgot-password-check-code - Forgot password check code screen
✅ /forgot-password-reset - Forgot password reset screen
✅ /social-login-form - Social login form screen
✅ /verify-email - Email verification screen

// Pending migration:
⏳ /account - Account screen
⏳ /events - Events screen
⏳ /news - News screen
// ... add other routes as needed
```

This gradual approach ensures a smooth transition while maintaining app stability and user experience.

## Advanced Topics

### Handling Complex Navigation Flows

For screens with complex navigation flows (multiple routes, conditional navigation):

```dart
class ComplexViewHybrid extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          ElevatedButton(
            onPressed: () async {
              // Complex navigation with conditions
              if (await someCondition()) {
                navigationService.offAllNamed(RouteNames.home);
              } else {
                navigationService.toNamed(RouteNames.error);
              }
            },
            child: const Text('Complex Navigation'),
          ),
        ],
      ),
    );
  }
}
```

### Deep Linking Support

go_router provides better deep linking support. For migrated routes:

```dart
// Deep link will automatically work for migrated routes
// Example: https://yourapp.com/profile/123
GoRoute(
  path: '/profile/:userId',
  builder: (context, state) {
    final userId = state.pathParameters['userId'];
    return ProfileViewHybrid(userId: userId);
  },
),
```

### Error Handling

Add error handling for navigation failures:

```dart
try {
  await navigationService.toNamed(RouteNames.example);
} catch (e) {
  // Handle navigation error
  debugPrint('Navigation failed: $e');
  // Fallback navigation
  Get.snackbar('Error', 'Navigation failed');
}
```

### Performance Considerations

1. **Lazy Loading**: Routes are only loaded when accessed
2. **Memory Management**: Properly dispose controllers in hybrid views
3. **State Management**: Ensure GetX controllers are properly managed

```dart
@override
void dispose() {
  // Always dispose GetX controllers
  Get.delete<ExampleController>();
  super.dispose();
}
```

## Migration Strategies

### Strategy 1: Bottom-Up Migration
Start with leaf screens (screens that don't navigate to many other screens):
1. Detail views
2. Settings screens
3. Profile screens
4. Form screens

### Strategy 2: Feature-Based Migration
Migrate entire features at once:
1. Authentication flow (login, register, forgot password)
2. User profile management
3. Content browsing
4. Settings and preferences

### Strategy 3: Critical Path Migration
Migrate the most important user flows first:
1. Login/Registration
2. Main navigation
3. Core features
4. Secondary features

## Code Quality Guidelines

### Naming Conventions
- Hybrid views: `*ViewHybrid`
- Route constants: Use descriptive names in camelCase
- File organization: Keep hybrid views in the same directory as original views

### Code Structure
```dart
class ExampleViewHybrid extends StatefulWidget {
  const ExampleViewHybrid({super.key});

  @override
  State<ExampleViewHybrid> createState() => _ExampleViewHybridState();
}

class _ExampleViewHybridState extends State<ExampleViewHybrid> {
  // 1. Controllers
  final ExampleController controller = Get.put(ExampleController());

  // 2. Services
  late final NavigationService navigationService;

  // 3. Lifecycle methods
  @override
  void initState() {
    super.initState();
    navigationService = NavigationService.instance;
  }

  @override
  void dispose() {
    Get.delete<ExampleController>();
    super.dispose();
  }

  // 4. Build method
  @override
  Widget build(BuildContext context) {
    return Scaffold(/* ... */);
  }

  // 5. Helper methods
  void _handleNavigation() {
    navigationService.toNamed(RouteNames.example);
  }
}
```

## Rollback Strategy

If issues arise during migration:

### Quick Rollback
1. Comment out the route from `migratedRoutes`
2. Add it back to `getxRoutes`
3. Update references back to original view

```dart
// Emergency rollback
static Set<String> get migratedRoutes => {
  login,
  register,
  // example, // Commented out for rollback
};

static Set<String> get getxRoutes => {
  example, // Added back for rollback
  // ... other routes
};
```

### Gradual Rollback
1. Keep both views available
2. Use feature flags to switch between them
3. Monitor for issues before full rollback

## Monitoring and Analytics

Track migration success:

```dart
// Add analytics to track navigation
void _trackNavigation(String route) {
  AnalyticsService.track('navigation_hybrid', {
    'route': route,
    'timestamp': DateTime.now().toIso8601String(),
  });
}
```

## Team Collaboration

### Code Review Checklist
- [ ] Hybrid view follows naming convention
- [ ] All navigation calls use NavigationService
- [ ] Route added to migrated routes
- [ ] Route removed from GetX routes (if applicable)
- [ ] Tests updated
- [ ] Documentation updated

### Communication
- Update team on migration progress
- Share learnings and issues
- Coordinate to avoid conflicts
- Plan migration schedule

This comprehensive guide should serve as your reference for migrating any screen from GetX router to go_router while maintaining full backward compatibility and app stability.
