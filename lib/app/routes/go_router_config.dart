import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../modules/login/views/login_view_hybrid.dart';
import '../modules/register/views/register_view_hybrid.dart';
import '../modules/forgot_password_email/views/forgot_password_email_view_hybrid.dart';
import '../modules/forgot_password_check_code/views/forgot_password_check_code_view_hybrid.dart';
import '../modules/forgot_password_reset/views/forgot_password_reset_view_hybrid.dart';
import '../modules/profile_edit/views/profile_edit_view.dart';
import '../modules/profile_edit/bindings/profile_edit_binding.dart';
import '../modules/social_login_form/views/social_login_form_view_hybrid.dart';
import '../modules/social_login_form/bindings/social_login_form_binding.dart';
import '../modules/verify_email/views/verify_email_view_hybrid.dart';
import '../modules/verify_email/bindings/verify_email_binding.dart';
import '../widgets/navigation_test_widget.dart';

/// go_router configuration for migrated routes
class GoRouterConfig {
  static final GlobalKey<NavigatorState> _rootNavigatorKey =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _shellNavigatorKey =
      GlobalKey<NavigatorState>();

  static GlobalKey<NavigatorState> get rootNavigatorKey => _rootNavigatorKey;
  static GlobalKey<NavigatorState> get shellNavigatorKey => _shellNavigatorKey;

  static GoRouter createRouter() {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/',
      debugLogDiagnostics: true,
      routes: [
        // Login route - simple route to start migration
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) {
            return const LoginViewHybrid();
          },
        ),

        // Register route
        GoRoute(
          path: '/register',
          name: 'register',
          builder: (context, state) {
            return const RegisterViewHybrid();
          },
        ),

        // Forgot Password Email route
        GoRoute(
          path: '/forgot-password-email',
          name: 'forgot-password-email',
          builder: (context, state) {
            return const ForgotPasswordEmailViewHybrid();
          },
        ),

        // Forgot Password Check Code route
        GoRoute(
          path: '/forgot-password-check-code',
          name: 'forgot-password-check-code',
          builder: (context, state) {
            return const ForgotPasswordCheckCodeViewHybrid();
          },
        ),

        // Forgot Password Reset route
        GoRoute(
          path: '/forgot-password-reset',
          name: 'forgot-password-reset',
          builder: (context, state) {
            return const ForgotPasswordResetViewHybrid();
          },
        ),

        // Profile Edit route - another simple route
        GoRoute(
          path: '/profile-edit',
          name: 'profile-edit',
          builder: (context, state) {
            // Initialize GetX binding for this route
            ProfileEditBinding().dependencies();
            return const ProfileEditView();
          },
        ),

        // Navigation test route
        GoRoute(
          path: '/navigation-test',
          name: 'navigation-test',
          builder: (context, state) {
            return const NavigationTestWidget();
          },
        ),

        // Social Login Form route
        GoRoute(
          path: '/social-login-form',
          name: 'social-login-form',
          builder: (context, state) {
            // Initialize GetX binding for this route
            SocialLoginFormBinding().dependencies();
            return const SocialLoginFormViewHybrid();
          },
        ),

        // Verify Email route
        GoRoute(
          path: '/verify-email',
          name: 'verify-email',
          builder: (context, state) {
            // Initialize GetX binding for this route
            VerifyEmailBinding().dependencies();
            return const VerifyEmailViewHybrid();
          },
        ),

        // Fallback route - redirects to GetX router
        GoRoute(
          path: '/',
          name: 'fallback',
          builder: (context, state) {
            // This should never be reached as we redirect to GetX
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
        ),
      ],

      // Error handling
      errorBuilder: (context, state) {
        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Route not found: ${state.uri}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context.go('/'),
                  child: const Text('Go Home'),
                ),
              ],
            ),
          ),
        );
      },

      // Redirect logic
      redirect: (context, state) {
        final location = state.uri.path;

        // List of routes handled by go_router
        const goRouterRoutes = {
          '/login',
          '/register',
          '/forgot-password-email',
          '/forgot-password-check-code',
          '/forgot-password-reset',
          '/profile-edit',
          '/navigation-test',
          '/social-login-form',
          '/verify-email',
        };

        // If this route is not handled by go_router, let GetX handle it
        if (!goRouterRoutes.contains(location)) {
          return null; // Let the route proceed normally
        }

        return null; // No redirect needed
      },
    );
  }
}

/// Extension to get arguments from go_router state
extension GoRouterStateExtension on GoRouterState {
  /// Get arguments passed from GetX-style navigation
  dynamic get arguments {
    final extra = this.extra;
    if (extra is Map<String, dynamic> && extra.containsKey('arguments')) {
      return extra['arguments'];
    }
    return null;
  }
}
