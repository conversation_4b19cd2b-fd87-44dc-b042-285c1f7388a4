# Auth Feature - Clean Architecture Implementation

This document describes the Clean Architecture implementation for the Auth feature while maintaining GetX for state management.

## Architecture Overview

The auth feature follows Clean Architecture principles with three main layers:

```
lib/app/features/auth/
├── domain/          # Business Logic Layer
├── data/            # Data Layer  
└── presentation/    # Presentation Layer
```

## Layer Responsibilities

### 1. Domain Layer (`domain/`)
**Pure business logic, framework-independent**

- **Entities** (`entities/`): Core business objects
  - `User`: Domain user entity
  - `AuthResult`: Authentication result
  - `AuthFailure`: Authentication error types

- **Repositories** (`repositories/`): Abstract contracts
  - `AuthRepository`: Interface defining auth operations

- **Use Cases** (`usecases/`): Business logic operations
  - `LoginUseCase`: Handle user login
  - `RegisterUseCase`: Handle user registration
  - `SocialLoginUseCase`: Handle social authentication
  - `ForgotPasswordUseCase`: Handle password recovery
  - `VerifyEmailUseCase`: Handle email verification

### 2. Data Layer (`data/`)
**Handles data sources and implements repository contracts**

- **Models** (`models/`): Data transfer objects
  - `UserModel`: Extends domain User with JSON serialization
  - `AuthResponseModel`: API response models

- **Data Sources** (`datasources/`): Data access implementations
  - `AuthRemoteDataSource`: API calls
  - `AuthLocalDataSource`: Local storage (SharedPreferences)

- **Repositories** (`repositories/`): Repository implementations
  - `AuthRepositoryImpl`: Implements domain AuthRepository

### 3. Presentation Layer (`presentation/`)
**UI and state management using GetX**

- **Controllers** (`controllers/`): GetX controllers
  - `AuthStateController`: Global auth state (singleton)
  - `LoginController`: Login page controller
  - `RegisterController`: Registration page controller

- **Pages** (`pages/`): UI screens
  - `LoginPageClean`: Clean architecture login page example

## Key Benefits

### 1. **Separation of Concerns**
- Business logic is isolated in the domain layer
- Data access is abstracted through repositories
- UI logic is separated from business logic

### 2. **Testability**
- Each layer can be tested independently
- Use cases contain pure business logic
- Dependencies are injected, making mocking easy

### 3. **Maintainability**
- Changes in one layer don't affect others
- Easy to add new features or modify existing ones
- Clear structure makes code easier to understand

### 4. **GetX Integration**
- Maintains reactive state management with GetX
- Global auth state through `AuthStateController`
- Reactive UI updates with `.obs` variables

## Usage Example

### 1. Initialize Dependencies
```dart
// In main.dart or app initialization
await AuthInjection.init();
```

### 2. Use in Controllers
```dart
class LoginController extends BaseScreenController {
  final LoginUseCase _loginUseCase;
  final AuthStateController _authStateController;

  LoginController(this._loginUseCase, this._authStateController);

  Future<bool> login() async {
    final result = await _loginUseCase.call(
      email: emailController.text,
      password: passwordController.text,
      deviceName: deviceName,
    );

    if (result is Success) {
      _authStateController.setUser(result.data.user);
      _authStateController.setToken(result.data.token);
      return true;
    }
    return false;
  }
}
```

### 3. Use in UI
```dart
class LoginPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<LoginController>();
    
    return Obx(() => ElevatedButton(
      onPressed: controller.isLoading.value ? null : () async {
        if (await controller.login()) {
          // Handle success
        }
      },
      child: controller.isLoading.value 
        ? CircularProgressIndicator()
        : Text('Login'),
    ));
  }
}
```

## Error Handling

The architecture provides robust error handling:

1. **Domain Level**: Typed failures (`AuthFailure` hierarchy)
2. **Data Level**: Exception mapping to domain failures
3. **Presentation Level**: User-friendly error messages

```dart
// Use case returns Result<Success, Failure>
final result = await loginUseCase.call(...);

if (result is Success) {
  // Handle success
} else if (result is Failure) {
  // Handle specific error types
  switch (result.error.runtimeType) {
    case InvalidCredentialsFailure:
      // Show invalid credentials message
      break;
    case NetworkFailure:
      // Show network error message
      break;
  }
}
```

## State Management

### Global Auth State
- `AuthStateController`: Singleton managing app-wide auth state
- Reactive properties for user, token, login status
- Persists state to local storage

### Page-Specific State
- Individual controllers for each page (Login, Register, etc.)
- Reactive UI state (loading, errors, form validation)
- Clean separation from business logic

## Migration Strategy

To migrate existing auth code to this clean architecture:

1. **Start with Use Cases**: Move business logic to use cases
2. **Create Repository Implementation**: Wrap existing API calls
3. **Update Controllers**: Use use cases instead of direct API calls
4. **Gradually Replace Views**: Update UI to use new controllers

## Testing Strategy

### Unit Tests
- **Domain Layer**: Test use cases with mocked repositories
- **Data Layer**: Test repository implementations with mocked data sources
- **Presentation Layer**: Test controllers with mocked use cases

### Integration Tests
- Test complete flows through all layers
- Test error handling scenarios
- Test state management integration

## Future Enhancements

1. **Add More Use Cases**: Logout, change password, etc.
2. **Implement Caching**: Add caching layer in repository
3. **Add Offline Support**: Handle offline scenarios
4. **Biometric Authentication**: Add biometric login use case
5. **Multi-factor Authentication**: Add MFA support

This clean architecture provides a solid foundation for the auth feature while maintaining the reactive benefits of GetX state management.
