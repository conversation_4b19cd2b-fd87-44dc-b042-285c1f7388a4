import '../../domain/entities/user.dart';

/// Data model for User that extends the domain entity
/// Handles JSON serialization/deserialization
class UserModel extends User {
  const UserModel({
    super.id,
    super.name,
    super.email,
    super.address,
    super.country,
    super.mobileNumber,
    super.birthDate,
    super.photo,
    super.createdAt,
    super.points,
    super.walletBalance,
  });

  /// Create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      address: json['address'],
      country: json['country'],
      mobileNumber: json['mobile_number'],
      birthDate: json['birth'] != null && json['birth'] != ""
          ? DateTime.parse(json['birth'])
          : null,
      photo: json['photo'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      points: json['points'],
      walletBalance: json['wallet_balance'],
    );
  }

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'address': address,
      'country': country,
      'mobile_number': mobileNumber,
      'birth': birthDate?.toIso8601String(),
      'photo': photo,
      'created_at': createdAt?.toIso8601String(),
      'points': points,
      'wallet_balance': walletBalance,
    };
  }

  /// Create UserModel from domain User entity
  factory UserModel.fromEntity(User user) {
    return UserModel(
      id: user.id,
      name: user.name,
      email: user.email,
      address: user.address,
      country: user.country,
      mobileNumber: user.mobileNumber,
      birthDate: user.birthDate,
      photo: user.photo,
      createdAt: user.createdAt,
      points: user.points,
      walletBalance: user.walletBalance,
    );
  }

  /// Convert to domain User entity
  User toEntity() {
    return User(
      id: id,
      name: name,
      email: email,
      address: address,
      country: country,
      mobileNumber: mobileNumber,
      birthDate: birthDate,
      photo: photo,
      createdAt: createdAt,
      points: points,
      walletBalance: walletBalance,
    );
  }

  /// Create a copy with updated fields
  UserModel copyWith({
    int? id,
    String? name,
    String? email,
    String? address,
    String? country,
    String? mobileNumber,
    DateTime? birthDate,
    String? photo,
    DateTime? createdAt,
    int? points,
    String? walletBalance,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      address: address ?? this.address,
      country: country ?? this.country,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      birthDate: birthDate ?? this.birthDate,
      photo: photo ?? this.photo,
      createdAt: createdAt ?? this.createdAt,
      points: points ?? this.points,
      walletBalance: walletBalance ?? this.walletBalance,
    );
  }
}
