import '../../domain/entities/auth_result.dart';
import 'user_model.dart';

/// Data model for authentication API responses
class AuthResponseModel {
  final bool success;
  final String? message;
  final String? token;
  final UserModel? user;
  final int? unreadNotificationCount;

  const AuthResponseModel({
    required this.success,
    this.message,
    this.token,
    this.user,
    this.unreadNotificationCount,
  });

  /// Create AuthResponseModel from JSON
  factory AuthResponseModel.fromJson(Map<String, dynamic> json) {
    return AuthResponseModel(
      success: json['success'] ?? false,
      message: json['message'],
      token: json['token'] ?? json['data']?['token'],
      user: json['user'] != null 
          ? UserModel.fromJson(json['user'])
          : json['data']?['user'] != null
              ? UserModel.fromJson(json['data']['user'])
              : null,
      unreadNotificationCount: json['unread_push_notification'] ?? 
          json['data']?['unread_push_notification'],
    );
  }

  /// Convert to domain AuthResult entity
  AuthResult? toAuthResult() {
    if (success && token != null && user != null) {
      return AuthResult(
        user: user!.toEntity(),
        token: token!,
        unreadNotificationCount: unreadNotificationCount,
      );
    }
    return null;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'token': token,
      'user': user?.toJson(),
      'unread_push_notification': unreadNotificationCount,
    };
  }
}

/// Model for simple success/failure responses
class SimpleResponseModel {
  final bool success;
  final String? message;

  const SimpleResponseModel({
    required this.success,
    this.message,
  });

  factory SimpleResponseModel.fromJson(Map<String, dynamic> json) {
    return SimpleResponseModel(
      success: json['success'] ?? false,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
    };
  }
}
