import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

import '../../../../constants/app_config.dart';
import '../../../../services/firebase_auth_service.dart';
import '../models/auth_response_model.dart';

/// Remote data source for authentication API calls
abstract class AuthRemoteDataSource {
  Future<AuthResponseModel> login({
    required String email,
    required String password,
    required String deviceName,
  });

  Future<SimpleResponseModel> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    required String deviceName,
    required String address,
    required String country,
    required String mobileNumber,
    DateTime? birthDate,
  });

  Future<AuthResponseModel> loginWithGoogle({
    required String deviceName,
  });

  Future<AuthResponseModel> loginWithApple({
    required String deviceName,
  });

  Future<SimpleResponseModel> sendPasswordResetEmail({
    required String email,
  });

  Future<SimpleResponseModel> verifyPasswordResetCode({
    required String code,
  });

  Future<SimpleResponseModel> resetPassword({
    required String code,
    required String password,
  });

  Future<AuthResponseModel> verifyEmail({
    required String email,
    required String code,
    required String deviceName,
  });
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final http.Client httpClient;

  AuthRemoteDataSourceImpl({required this.httpClient});

  @override
  Future<AuthResponseModel> login({
    required String email,
    required String password,
    required String deviceName,
  }) async {
    final response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}auth/login'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
      },
      body: jsonEncode({
        'email': email,
        'password': password,
        'device_name': deviceName,
      }),
    );

    if (_isValidResponseCode(response.statusCode)) {
      return AuthResponseModel.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Server error: ${response.statusCode}');
    }
  }

  @override
  Future<SimpleResponseModel> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    required String deviceName,
    required String address,
    required String country,
    required String mobileNumber,
    DateTime? birthDate,
  }) async {
    String? birth =
        birthDate != null ? DateFormat('yyyy-MM-dd').format(birthDate) : "";

    final response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}auth/register'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
      },
      body: jsonEncode({
        'name': name,
        'email': email,
        'password': password,
        'password_confirmation': passwordConfirmation,
        'device_name': deviceName,
        'address': address,
        'country': country,
        'mobile_number': mobileNumber,
        'birth': birth,
      }),
    );

    if (_isValidResponseCode(response.statusCode)) {
      return SimpleResponseModel.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Server error: ${response.statusCode}');
    }
  }

  @override
  Future<AuthResponseModel> loginWithGoogle({
    required String deviceName,
  }) async {
    // Get Google ID token from Firebase
    final idToken = await FirebaseAuthService.signInWithGoogle();
    if (idToken == null) {
      throw Exception('Google sign-in failed');
    }

    final response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}auth/signInWithGoogle'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
      },
      body: jsonEncode({
        'tokenId': idToken,
        'deviceName': deviceName,
      }),
    );

    if (_isValidResponseCode(response.statusCode)) {
      return AuthResponseModel.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Server error: ${response.statusCode}');
    }
  }

  @override
  Future<AuthResponseModel> loginWithApple({
    required String deviceName,
  }) async {
    // Get Apple credentials from Firebase
    final userCredential = await FirebaseAuthService.signInWithApple();
    final idToken = await userCredential.user?.getIdToken();
    final displayName = userCredential.user?.displayName ?? "";

    if (idToken == null) {
      throw Exception('Apple sign-in failed');
    }

    final response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}auth/signInWithApple'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
      },
      body: jsonEncode({
        'tokenId': idToken,
        'deviceName': deviceName,
        'displayName': displayName,
      }),
    );

    if (_isValidResponseCode(response.statusCode)) {
      return AuthResponseModel.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Server error: ${response.statusCode}');
    }
  }

  @override
  Future<SimpleResponseModel> sendPasswordResetEmail({
    required String email,
  }) async {
    final response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}resetPassword/sendResetCode'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
      },
      body: jsonEncode({'email': email}),
    );

    if (_isValidResponseCode(response.statusCode)) {
      return SimpleResponseModel.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Server error: ${response.statusCode}');
    }
  }

  @override
  Future<SimpleResponseModel> verifyPasswordResetCode({
    required String code,
  }) async {
    final response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}resetPassword/checkCode'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
      },
      body: jsonEncode({'code': code}),
    );

    if (_isValidResponseCode(response.statusCode)) {
      return SimpleResponseModel.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Server error: ${response.statusCode}');
    }
  }

  @override
  Future<SimpleResponseModel> resetPassword({
    required String code,
    required String password,
  }) async {
    final response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}resetPassword/resetPassword'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
      },
      body: jsonEncode({
        'code': code,
        'password': password,
        'password_confirm': password,
      }),
    );

    if (_isValidResponseCode(response.statusCode)) {
      return SimpleResponseModel.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Server error: ${response.statusCode}');
    }
  }

  @override
  Future<AuthResponseModel> verifyEmail({
    required String email,
    required String code,
    required String deviceName,
  }) async {
    final response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}auth/verifyEmail'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
      },
      body: jsonEncode({
        'email': email,
        'code': code,
        'device_name': deviceName,
      }),
    );

    if (_isValidResponseCode(response.statusCode)) {
      return AuthResponseModel.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Server error: ${response.statusCode}');
    }
  }

  bool _isValidResponseCode(int statusCode) {
    return statusCode >= 200 && statusCode < 300;
  }
}
