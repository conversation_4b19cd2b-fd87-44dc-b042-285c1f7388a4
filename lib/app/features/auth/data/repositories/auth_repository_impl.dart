import '../../domain/entities/auth_result.dart';
import '../../domain/entities/auth_failure.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_local_datasource.dart';
import '../datasources/auth_remote_datasource.dart';
import '../models/user_model.dart';

/// Implementation of AuthRepository
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Result<AuthResult, AuthFailure>> login({
    required String email,
    required String password,
    required String deviceName,
  }) async {
    try {
      final response = await remoteDataSource.login(
        email: email,
        password: password,
        deviceName: deviceName,
      );

      if (response.success) {
        final authResult = response.toAuthResult();
        if (authResult != null) {
          return Success(authResult);
        } else {
          return const Failure(ServerFailure('Invalid response format'));
        }
      } else {
        return Failure(_mapErrorMessage(response.message));
      }
    } catch (e) {
      return Failure(_mapException(e));
    }
  }

  @override
  Future<Result<bool, AuthFailure>> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    required String deviceName,
    required String address,
    required String country,
    required String mobileNumber,
    DateTime? birthDate,
  }) async {
    try {
      final response = await remoteDataSource.register(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
        deviceName: deviceName,
        address: address,
        country: country,
        mobileNumber: mobileNumber,
        birthDate: birthDate,
      );

      if (response.success) {
        return const Success(true);
      } else {
        return Failure(_mapErrorMessage(response.message));
      }
    } catch (e) {
      return Failure(_mapException(e));
    }
  }

  @override
  Future<Result<AuthResult, AuthFailure>> loginWithGoogle({
    required String deviceName,
  }) async {
    try {
      final response = await remoteDataSource.loginWithGoogle(
        deviceName: deviceName,
      );

      if (response.success) {
        final authResult = response.toAuthResult();
        if (authResult != null) {
          return Success(authResult);
        } else {
          return const Failure(ServerFailure('Invalid response format'));
        }
      } else {
        return const Failure(SocialLoginFailure('Google login failed'));
      }
    } catch (e) {
      return Failure(_mapException(e));
    }
  }

  @override
  Future<Result<AuthResult, AuthFailure>> loginWithApple({
    required String deviceName,
  }) async {
    try {
      final response = await remoteDataSource.loginWithApple(
        deviceName: deviceName,
      );

      if (response.success) {
        final authResult = response.toAuthResult();
        if (authResult != null) {
          return Success(authResult);
        } else {
          return const Failure(ServerFailure('Invalid response format'));
        }
      } else {
        return const Failure(SocialLoginFailure('Apple login failed'));
      }
    } catch (e) {
      return Failure(_mapException(e));
    }
  }

  @override
  Future<Result<bool, AuthFailure>> sendPasswordResetEmail({
    required String email,
  }) async {
    try {
      final response = await remoteDataSource.sendPasswordResetEmail(
        email: email,
      );

      if (response.success) {
        return const Success(true);
      } else {
        return Failure(_mapErrorMessage(response.message));
      }
    } catch (e) {
      return Failure(_mapException(e));
    }
  }

  @override
  Future<Result<bool, AuthFailure>> verifyPasswordResetCode({
    required String code,
  }) async {
    try {
      final response = await remoteDataSource.verifyPasswordResetCode(
        code: code,
      );

      if (response.success) {
        return const Success(true);
      } else {
        return Failure(_mapErrorMessage(response.message));
      }
    } catch (e) {
      return Failure(_mapException(e));
    }
  }

  @override
  Future<Result<bool, AuthFailure>> resetPassword({
    required String code,
    required String password,
  }) async {
    try {
      final response = await remoteDataSource.resetPassword(
        code: code,
        password: password,
      );

      if (response.success) {
        return const Success(true);
      } else {
        return Failure(_mapErrorMessage(response.message));
      }
    } catch (e) {
      return Failure(_mapException(e));
    }
  }

  @override
  Future<Result<AuthResult, AuthFailure>> verifyEmail({
    required String email,
    required String code,
    required String deviceName,
  }) async {
    try {
      final response = await remoteDataSource.verifyEmail(
        email: email,
        code: code,
        deviceName: deviceName,
      );

      if (response.success) {
        final authResult = response.toAuthResult();
        if (authResult != null) {
          return Success(authResult);
        } else {
          return const Failure(ServerFailure('Invalid response format'));
        }
      } else {
        return Failure(_mapErrorMessage(response.message));
      }
    } catch (e) {
      return Failure(_mapException(e));
    }
  }

  @override
  Future<User?> getCurrentUser() async {
    final userModel = await localDataSource.getCurrentUser();
    return userModel?.toEntity();
  }

  @override
  Future<void> saveUser(User user) async {
    final userModel = UserModel.fromEntity(user);
    await localDataSource.saveUser(userModel);
  }

  @override
  Future<String?> getCurrentToken() async {
    return await localDataSource.getCurrentToken();
  }

  @override
  Future<void> saveToken(String token) async {
    await localDataSource.saveToken(token);
  }

  @override
  Future<void> clearAuthData() async {
    await localDataSource.clearAuthData();
  }

  @override
  Future<bool> isLoggedIn() async {
    return await localDataSource.isLoggedIn();
  }

  /// Map error messages to appropriate AuthFailure types
  AuthFailure _mapErrorMessage(String? message) {
    if (message == null) {
      return const GenericAuthFailure('Unknown error occurred');
    }

    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains('invalid credentials') ||
        lowerMessage.contains('wrong password') ||
        lowerMessage.contains('incorrect password')) {
      return InvalidCredentialsFailure(message);
    }

    if (lowerMessage.contains('user not found') ||
        lowerMessage.contains('email not found')) {
      return UserNotFoundFailure(message);
    }

    if (lowerMessage.contains('email already exists') ||
        lowerMessage.contains('already registered')) {
      return EmailAlreadyExistsFailure(message);
    }

    if (lowerMessage.contains('invalid code') ||
        lowerMessage.contains('verification code')) {
      return InvalidVerificationCodeFailure(message);
    }

    return GenericAuthFailure(message);
  }

  /// Map exceptions to appropriate AuthFailure types
  AuthFailure _mapException(dynamic exception) {
    final message = exception.toString();

    if (message.contains('SocketException') ||
        message.contains('NetworkException')) {
      return const NetworkFailure('No internet connection');
    }

    if (message.contains('TimeoutException')) {
      return const NetworkFailure('Request timeout');
    }

    if (message.contains('Server error')) {
      return const ServerFailure('Server is currently unavailable');
    }

    return GenericAuthFailure(message);
  }
}
