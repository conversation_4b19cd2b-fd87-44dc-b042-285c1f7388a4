import 'user.dart';

/// Domain entity representing the result of authentication operations
class AuthResult {
  final User user;
  final String token;
  final int? unreadNotificationCount;

  const AuthResult({
    required this.user,
    required this.token,
    this.unreadNotificationCount,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthResult &&
        other.user == user &&
        other.token == token &&
        other.unreadNotificationCount == unreadNotificationCount;
  }

  @override
  int get hashCode {
    return Object.hash(user, token, unreadNotificationCount);
  }

  @override
  String toString() {
    return 'AuthResult(user: $user, token: $token, unreadNotificationCount: $unreadNotificationCount)';
  }
}
