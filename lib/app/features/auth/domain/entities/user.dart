/// Domain entity representing a user
/// This is the core business object, independent of any external concerns
class User {
  final int? id;
  final String? name;
  final String? email;
  final String? address;
  final String? country;
  final String? mobileNumber;
  final DateTime? birthDate;
  final String? photo;
  final DateTime? createdAt;
  final int? points;
  final String? walletBalance;

  const User({
    this.id,
    this.name,
    this.email,
    this.address,
    this.country,
    this.mobileNumber,
    this.birthDate,
    this.photo,
    this.createdAt,
    this.points,
    this.walletBalance,
  });

  /// Check if user has all required fields filled
  bool get hasRequiredFields {
    return name != null &&
        name!.isNotEmpty &&
        email != null &&
        email!.isNotEmpty &&
        country != null &&
        country!.isNotEmpty &&
        mobileNumber != null &&
        mobileNumber!.isNotEmpty;
  }

  /// Create a copy of user with updated fields
  User copyWith({
    int? id,
    String? name,
    String? email,
    String? address,
    String? country,
    String? mobileNumber,
    DateTime? birthDate,
    String? photo,
    DateTime? createdAt,
    int? points,
    String? walletBalance,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      address: address ?? this.address,
      country: country ?? this.country,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      birthDate: birthDate ?? this.birthDate,
      photo: photo ?? this.photo,
      createdAt: createdAt ?? this.createdAt,
      points: points ?? this.points,
      walletBalance: walletBalance ?? this.walletBalance,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.address == address &&
        other.country == country &&
        other.mobileNumber == mobileNumber &&
        other.birthDate == birthDate &&
        other.photo == photo &&
        other.createdAt == createdAt &&
        other.points == points &&
        other.walletBalance == walletBalance;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      email,
      address,
      country,
      mobileNumber,
      birthDate,
      photo,
      createdAt,
      points,
      walletBalance,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, country: $country)';
  }
}
