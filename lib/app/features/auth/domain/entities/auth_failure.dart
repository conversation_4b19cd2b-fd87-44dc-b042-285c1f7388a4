/// Domain entity representing authentication failures
abstract class AuthFailure {
  final String message;
  
  const AuthFailure(this.message);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthFailure && other.message == message;
  }

  @override
  int get hashCode => message.hashCode;

  @override
  String toString() => 'AuthFailure(message: $message)';
}

/// Server-related authentication failures
class ServerFailure extends AuthFailure {
  const ServerFailure(super.message);
}

/// Network-related authentication failures
class NetworkFailure extends AuthFailure {
  const NetworkFailure(super.message);
}

/// Invalid credentials failure
class InvalidCredentialsFailure extends AuthFailure {
  const InvalidCredentialsFailure(super.message);
}

/// User not found failure
class UserNotFoundFailure extends AuthFailure {
  const UserNotFoundFailure(super.message);
}

/// Email already exists failure
class EmailAlreadyExistsFailure extends AuthFailure {
  const EmailAlreadyExistsFailure(super.message);
}

/// Invalid verification code failure
class InvalidVerificationCodeFailure extends AuthFailure {
  const InvalidVerificationCodeFailure(super.message);
}

/// Social login failure
class SocialLoginFailure extends AuthFailure {
  const SocialLoginFailure(super.message);
}

/// Generic authentication failure
class GenericAuthFailure extends AuthFailure {
  const GenericAuthFailure(super.message);
}
