import '../entities/auth_result.dart';
import '../entities/auth_failure.dart';
import '../repositories/auth_repository.dart';

/// Use case for user login
class LoginUseCase {
  final AuthRepository _repository;

  const LoginUseCase(this._repository);

  /// Execute login with email and password
  Future<Result<AuthResult, AuthFailure>> call({
    required String email,
    required String password,
    required String deviceName,
  }) async {
    // Validate input
    if (email.isEmpty) {
      return const Failure(GenericAuthFailure('Email is required'));
    }
    
    if (password.isEmpty) {
      return const Failure(GenericAuthFailure('Password is required'));
    }

    if (!_isValidEmail(email)) {
      return const Failure(GenericAuthFailure('Invalid email format'));
    }

    // Call repository
    final result = await _repository.login(
      email: email,
      password: password,
      deviceName: deviceName,
    );

    // Handle success case - save user data locally
    if (result is Success<AuthResult, AuthFailure>) {
      await _repository.saveUser(result.data.user);
      await _repository.saveToken(result.data.token);
    }

    return result;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}
