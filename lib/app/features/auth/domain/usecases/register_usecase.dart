import '../entities/auth_failure.dart';
import '../repositories/auth_repository.dart';

/// Use case for user registration
class RegisterUseCase {
  final AuthRepository _repository;

  const RegisterUseCase(this._repository);

  /// Execute user registration
  Future<Result<bool, AuthFailure>> call({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    required String deviceName,
    required String address,
    required String country,
    required String mobileNumber,
    DateTime? birthDate,
  }) async {
    // Validate input
    final validationResult = _validateInput(
      name: name,
      email: email,
      password: password,
      passwordConfirmation: passwordConfirmation,
      address: address,
      country: country,
      mobileNumber: mobileNumber,
    );

    if (validationResult != null) {
      return Failure(validationResult);
    }

    // Call repository
    return await _repository.register(
      name: name,
      email: email,
      password: password,
      passwordConfirmation: passwordConfirmation,
      deviceName: deviceName,
      address: address,
      country: country,
      mobileNumber: mobileNumber,
      birthDate: birthDate,
    );
  }

  AuthFailure? _validateInput({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    required String address,
    required String country,
    required String mobileNumber,
  }) {
    if (name.isEmpty) {
      return const GenericAuthFailure('Name is required');
    }

    if (email.isEmpty) {
      return const GenericAuthFailure('Email is required');
    }

    if (!_isValidEmail(email)) {
      return const GenericAuthFailure('Invalid email format');
    }

    if (password.isEmpty) {
      return const GenericAuthFailure('Password is required');
    }

    if (password.length < 6) {
      return const GenericAuthFailure('Password must be at least 6 characters');
    }

    if (password != passwordConfirmation) {
      return const GenericAuthFailure('Password confirmation does not match');
    }

    if (address.isEmpty) {
      return const GenericAuthFailure('Address is required');
    }

    if (country.isEmpty) {
      return const GenericAuthFailure('Country is required');
    }

    if (mobileNumber.isEmpty) {
      return const GenericAuthFailure('Mobile number is required');
    }

    return null;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}
