import '../entities/auth_result.dart';
import '../entities/auth_failure.dart';
import '../repositories/auth_repository.dart';

/// Use case for social login (Google, Apple)
class SocialLoginUseCase {
  final AuthRepository _repository;

  const SocialLoginUseCase(this._repository);

  /// Execute Google login
  Future<Result<AuthResult, AuthFailure>> loginWithGoogle({
    required String deviceName,
  }) async {
    if (deviceName.isEmpty) {
      return const Failure(GenericAuthFailure('Device name is required'));
    }

    final result = await _repository.loginWithGoogle(deviceName: deviceName);

    // Handle success case - save user data locally
    if (result is Success<AuthResult, AuthFailure>) {
      await _repository.saveUser(result.data.user);
      await _repository.saveToken(result.data.token);
    }

    return result;
  }

  /// Execute Apple login
  Future<Result<AuthResult, AuthFailure>> loginWithApple({
    required String deviceName,
  }) async {
    if (deviceName.isEmpty) {
      return const Failure(GenericAuthFailure('Device name is required'));
    }

    final result = await _repository.loginWithApple(deviceName: deviceName);

    // Handle success case - save user data locally
    if (result is Success<AuthResult, AuthFailure>) {
      await _repository.saveUser(result.data.user);
      await _repository.saveToken(result.data.token);
    }

    return result;
  }
}
