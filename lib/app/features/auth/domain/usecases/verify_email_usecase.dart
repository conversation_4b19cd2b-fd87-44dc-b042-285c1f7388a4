import '../entities/auth_result.dart';
import '../entities/auth_failure.dart';
import '../repositories/auth_repository.dart';

/// Use case for email verification
class VerifyEmailUseCase {
  final AuthRepository _repository;

  const VerifyEmailUseCase(this._repository);

  /// Verify email with code
  Future<Result<AuthResult, AuthFailure>> call({
    required String email,
    required String code,
    required String deviceName,
  }) async {
    if (email.isEmpty) {
      return const Failure(GenericAuthFailure('Email is required'));
    }

    if (code.isEmpty) {
      return const Failure(GenericAuthFailure('Verification code is required'));
    }

    if (deviceName.isEmpty) {
      return const Failure(GenericAuthFailure('Device name is required'));
    }

    if (!_isValidEmail(email)) {
      return const Failure(GenericAuthFailure('Invalid email format'));
    }

    final result = await _repository.verifyEmail(
      email: email,
      code: code,
      deviceName: deviceName,
    );

    // Handle success case - save user data locally
    if (result is Success<AuthResult, AuthFailure>) {
      await _repository.saveUser(result.data.user);
      await _repository.saveToken(result.data.token);
    }

    return result;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}
