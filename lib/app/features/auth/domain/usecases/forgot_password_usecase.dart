import '../entities/auth_failure.dart';
import '../repositories/auth_repository.dart';

/// Use case for forgot password functionality
class ForgotPasswordUseCase {
  final AuthRepository _repository;

  const ForgotPasswordUseCase(this._repository);

  /// Send password reset email
  Future<Result<bool, AuthFailure>> sendResetEmail({
    required String email,
  }) async {
    if (email.isEmpty) {
      return const Failure(GenericAuthFailure('Email is required'));
    }

    if (!_isValidEmail(email)) {
      return const Failure(GenericAuthFailure('Invalid email format'));
    }

    return await _repository.sendPasswordResetEmail(email: email);
  }

  /// Verify password reset code
  Future<Result<bool, AuthFailure>> verifyResetCode({
    required String code,
  }) async {
    if (code.isEmpty) {
      return const Failure(GenericAuthFailure('Verification code is required'));
    }

    return await _repository.verifyPasswordResetCode(code: code);
  }

  /// Reset password with code
  Future<Result<bool, AuthFailure>> resetPassword({
    required String code,
    required String password,
    required String passwordConfirmation,
  }) async {
    if (code.isEmpty) {
      return const Failure(GenericAuthFailure('Verification code is required'));
    }

    if (password.isEmpty) {
      return const Failure(GenericAuthFailure('Password is required'));
    }

    if (password.length < 6) {
      return const Failure(GenericAuthFailure('Password must be at least 6 characters'));
    }

    if (password != passwordConfirmation) {
      return const Failure(GenericAuthFailure('Password confirmation does not match'));
    }

    return await _repository.resetPassword(code: code, password: password);
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}
