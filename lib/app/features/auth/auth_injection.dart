import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import 'data/datasources/auth_local_datasource.dart';
import 'data/datasources/auth_remote_datasource.dart';
import 'data/repositories/auth_repository_impl.dart';
import 'domain/repositories/auth_repository.dart';
import 'domain/usecases/forgot_password_usecase.dart';
import 'domain/usecases/login_usecase.dart';
import 'domain/usecases/register_usecase.dart';
import 'domain/usecases/social_login_usecase.dart';
import 'domain/usecases/verify_email_usecase.dart';
import 'presentation/controllers/auth_state_controller.dart';
import 'presentation/controllers/login_controller.dart';
import 'presentation/controllers/register_controller.dart';
import 'presentation/controllers/forgot_password_controller.dart';
import 'presentation/controllers/verify_email_controller.dart';
import 'presentation/controllers/guest_controller.dart';
import 'presentation/controllers/social_form_controller.dart';

/// Dependency injection setup for Auth feature
class AuthInjection {
  static Future<void> init() async {
    // External dependencies
    final sharedPreferences = await SharedPreferences.getInstance();
    Get.put<SharedPreferences>(sharedPreferences);
    Get.put<http.Client>(http.Client());

    // Data sources
    Get.lazyPut<AuthLocalDataSource>(
      () => AuthLocalDataSourceImpl(
        sharedPreferences: Get.find<SharedPreferences>(),
      ),
    );

    Get.lazyPut<AuthRemoteDataSource>(
      () => AuthRemoteDataSourceImpl(
        httpClient: Get.find<http.Client>(),
      ),
    );

    // Repository
    Get.lazyPut<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: Get.find<AuthRemoteDataSource>(),
        localDataSource: Get.find<AuthLocalDataSource>(),
      ),
    );

    // Use cases
    Get.lazyPut<LoginUseCase>(
      () => LoginUseCase(Get.find<AuthRepository>()),
    );

    Get.lazyPut<RegisterUseCase>(
      () => RegisterUseCase(Get.find<AuthRepository>()),
    );

    Get.lazyPut<SocialLoginUseCase>(
      () => SocialLoginUseCase(Get.find<AuthRepository>()),
    );

    Get.lazyPut<ForgotPasswordUseCase>(
      () => ForgotPasswordUseCase(Get.find<AuthRepository>()),
    );

    Get.lazyPut<VerifyEmailUseCase>(
      () => VerifyEmailUseCase(Get.find<AuthRepository>()),
    );

    // Global auth state controller (singleton)
    Get.put<AuthStateController>(
      AuthStateController(Get.find<AuthRepository>()),
      permanent: true,
    );

    // Presentation controllers (lazy loaded)
    Get.lazyPut<LoginController>(
      () => LoginController(
        Get.find<LoginUseCase>(),
        Get.find<SocialLoginUseCase>(),
        Get.find<AuthStateController>(),
      ),
    );

    Get.lazyPut<RegisterController>(
      () => RegisterController(
        Get.find<RegisterUseCase>(),
        Get.find<SocialLoginUseCase>(),
        Get.find<AuthStateController>(),
      ),
    );

    Get.lazyPut<ForgotPasswordController>(
      () => ForgotPasswordController(
        Get.find<ForgotPasswordUseCase>(),
      ),
    );

    Get.lazyPut<ForgotPasswordCheckCodeController>(
      () => ForgotPasswordCheckCodeController(
        Get.find<ForgotPasswordUseCase>(),
      ),
    );

    Get.lazyPut<ForgotPasswordResetController>(
      () => ForgotPasswordResetController(
        Get.find<ForgotPasswordUseCase>(),
      ),
    );

    Get.lazyPut<VerifyEmailController>(
      () => VerifyEmailController(
        Get.find<VerifyEmailUseCase>(),
        Get.find<AuthStateController>(),
      ),
    );

    Get.lazyPut<GuestController>(
      () => GuestController(),
    );

    Get.lazyPut<SocialFormController>(
      () => SocialFormController(
        Get.find<RegisterUseCase>(),
        Get.find<AuthStateController>(),
      ),
    );
  }

  /// Clean up auth dependencies
  static void dispose() {
    // Controllers
    Get.delete<LoginController>();
    Get.delete<RegisterController>();
    Get.delete<ForgotPasswordController>();
    Get.delete<ForgotPasswordCheckCodeController>();
    Get.delete<ForgotPasswordResetController>();
    Get.delete<VerifyEmailController>();
    Get.delete<GuestController>();
    Get.delete<SocialFormController>();

    // Use cases
    Get.delete<VerifyEmailUseCase>();
    Get.delete<ForgotPasswordUseCase>();
    Get.delete<SocialLoginUseCase>();
    Get.delete<RegisterUseCase>();
    Get.delete<LoginUseCase>();

    // Repository and data sources
    Get.delete<AuthRepository>();
    Get.delete<AuthRemoteDataSource>();
    Get.delete<AuthLocalDataSource>();

    // Note: Don't delete AuthStateController as it's permanent
  }
}
