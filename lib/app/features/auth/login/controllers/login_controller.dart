import 'dart:convert';

import 'package:automoment/app/services/firebase_auth_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../../controllers/base_screen_controller.dart';
import '../../../../constants/app_config.dart';
import '../../../../controllers/user_controller.dart';
import '../../../../helpers/device_util.dart';
import '../../../../models/user_model.dart' as user_model;
import '../../../../services/api_client.dart';

class LoginController extends BaseScreenController {
  var isPasswordHidden = true.obs;
  var isChecked = false.obs;
  var message = "";
  var token = "";
  String? deviceName;

  final formKey = GlobalKey<FormState>().obs;

  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  final UserController userController = Get.put(UserController());

  @override
  String get screenName => 'Login';

  @override
  Future<void> onInit() async {
    super.onInit();
    deviceName = await DeviceUtil().getDeviceName();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    // Screen view is automatically logged by BaseScreenController
  }

  @override
  void onClose() {
    super.onClose();

    emailController.dispose();
    passwordController.dispose();
  }

  Future<bool> login() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.login(emailController.text,
          passwordController.text, deviceName ?? AppConfig.genericDevice);

      bool success = jsonDecode(response)['success'];
      EasyLoading.dismiss();
      if (success) {
        token = jsonDecode(response)['token'];
        message = "";
        var user = user_model.User.fromJson(jsonDecode(response)['user']);

        userController.setIsLoggedIn(true);
        userController.setUser(user);
        userController.setToken(token);

        userController.unreadNotificationCount.value =
            jsonDecode(response)['unread_push_notification'];

        // Log login event
        await analytics.logLogin(method: 'email');

        // Set user properties for analytics
        await analytics.setUserProperties(
          userId: user.id.toString(),
          country: user.country,
        );

        return true;
      } else {
        message = jsonDecode(response)['message'];
        return false;
      }
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<bool> signInWithGoogle() async {
    try {
      String? idToken = await FirebaseAuthService.signInWithGoogle();

      if (idToken != null) {
        EasyLoading.show(status: 'Please wait...');
        var response = await ApiClient.signInWithGoogle(
            idToken, deviceName ?? AppConfig.genericDevice);

        bool success = jsonDecode(response)['success'];
        EasyLoading.dismiss();
        if (success) {
          token = jsonDecode(response)['token'];
          message = "";
          var user = user_model.User.fromJson(jsonDecode(response)['user']);

          userController.setIsLoggedIn(true);
          userController.setUser(user);
          userController.setToken(token);
          userController.isLoggedInWithFirebase.value = true;

          userController.unreadNotificationCount.value =
              jsonDecode(response)['unread_push_notification'];

          // Log login event with Google method
          await analytics.logLogin(method: 'google');

          // Set user properties for analytics
          await analytics.setUserProperties(
            userId: user.id.toString(),
            country: user.country,
          );

          return true;
        } else {
          message = "Unable to login with Google";
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  Future<bool> signInWithApple() async {
    try {
      UserCredential userCredential =
          await FirebaseAuthService.signInWithApple();
      String? idToken = await userCredential.user?.getIdToken();
      String displayName = userCredential.user?.displayName ?? "";

      if (idToken != null) {
        EasyLoading.show(status: 'Please wait...');
        var response = await ApiClient.signInWithApple(
            idToken, deviceName ?? AppConfig.genericDevice, displayName);

        bool success = jsonDecode(response)['success'];
        EasyLoading.dismiss();
        if (success) {
          token = jsonDecode(response)['token'];
          message = "";
          var user = user_model.User.fromJson(jsonDecode(response)['user']);

          userController.setIsLoggedIn(true);
          userController.setUser(user);
          userController.setToken(token);
          userController.isLoggedInWithFirebase.value = true;

          userController.unreadNotificationCount.value =
              jsonDecode(response)['unread_push_notification'];

          // Log login event with Apple method
          await analytics.logLogin(method: 'apple');

          // Set user properties for analytics
          await analytics.setUserProperties(
            userId: user.id.toString(),
            country: user.country,
          );

          return true;
        } else {
          message = "Unable to login with Apple";
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  bool isAllRequiredUserFieldsFilled() {
    var user = userController.getUser();

    if (user.name != null &&
        user.name!.isNotEmpty &&
        user.email != null &&
        user.email!.isNotEmpty &&
        user.country != null &&
        user.country!.isNotEmpty &&
        user.mobileNumber != null &&
        user.mobileNumber!.isNotEmpty) {
      return true;
    } else {
      return false;
    }
  }
}
