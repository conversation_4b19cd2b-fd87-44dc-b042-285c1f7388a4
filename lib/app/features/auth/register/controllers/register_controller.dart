import 'dart:convert';

import 'package:automoment/app/helpers/device_util.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../../controllers/base_screen_controller.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

import '../../../../constants/app_config.dart';
import '../../../../controllers/user_controller.dart';
import '../../../../models/user_model.dart' as user_model;
import '../../../../services/api_client.dart';
import '../../../../services/firebase_auth_service.dart';

class RegisterController extends BaseScreenController {
  @override
  String get screenName => 'Register';

  var isPasswordHidden = true.obs;
  var isConfirmPasswordHidden = true.obs;
  var isChecked = false.obs;
  var message = "";
  var token = "";

  final formKey = GlobalKey<FormState>().obs;

  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController passwordConfirmController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController mobileNumberController = TextEditingController();
  var countryController = TextEditingController().obs;
  var dateOfBirthController = TextEditingController().obs;
  DateTime? birthDate;
  PhoneNumber mobileNumber = PhoneNumber(isoCode: 'SG');
  String? deviceName;

  final UserController userController = Get.put(UserController());

  @override
  Future<void> onInit() async {
    super.onInit();
    deviceName = await DeviceUtil().getDeviceName();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    passwordConfirmController.dispose();
    addressController.dispose();
    mobileNumberController.dispose();
    countryController.value.dispose();
    dateOfBirthController.value.dispose();
  }

  Future<bool> register() async {
    try {
      EasyLoading.show(status: 'Please wait...');

      var response = await ApiClient.register(
        nameController.text,
        emailController.text,
        passwordController.text,
        passwordConfirmController.text,
        deviceName ?? AppConfig.genericDevice,
        addressController.text,
        countryController.value.text,
        mobileNumberController.text,
        birthDate,
      );

      bool success = jsonDecode(response)['success'] ?? false;
      EasyLoading.dismiss();

      if (success) {
        return true;
      } else {
        message = jsonDecode(response)['message'];
        return false;
      }
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<bool> signInWithGoogle() async {
    try {
      String? idToken = await FirebaseAuthService.signInWithGoogle();

      if (idToken != null) {
        EasyLoading.show(status: 'Please wait...');
        var response = await ApiClient.signInWithGoogle(
            idToken, deviceName ?? AppConfig.genericDevice);

        bool success = jsonDecode(response)['success'];
        EasyLoading.dismiss();
        if (success) {
          token = jsonDecode(response)['token'];
          message = "";
          var user = user_model.User.fromJson(jsonDecode(response)['user']);

          userController.setIsLoggedIn(true);
          userController.setUser(user);
          userController.setToken(token);
          userController.isLoggedInWithFirebase.value = true;

          userController.unreadNotificationCount.value =
              jsonDecode(response)['unread_push_notification'];

          return true;
        } else {
          message = "Unable to login with Google";
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  Future<bool> signInWithApple() async {
    try {
      UserCredential userCredential =
          await FirebaseAuthService.signInWithApple();
      String? idToken = await userCredential.user?.getIdToken();
      String displayName = userCredential.user?.displayName ?? "";

      if (idToken != null) {
        EasyLoading.show(status: 'Please wait...');
        var response = await ApiClient.signInWithApple(
            idToken, deviceName ?? AppConfig.genericDevice, displayName);

        bool success = jsonDecode(response)['success'];
        EasyLoading.dismiss();
        if (success) {
          token = jsonDecode(response)['token'];
          message = "";
          var user = user_model.User.fromJson(jsonDecode(response)['user']);

          userController.setIsLoggedIn(true);
          userController.setUser(user);
          userController.setToken(token);
          userController.isLoggedInWithFirebase.value = true;

          userController.unreadNotificationCount.value =
              jsonDecode(response)['unread_push_notification'];

          return true;
        } else {
          message = "Unable to login with Google";
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  bool isAllRequiredUserFieldsFilled() {
    var user = userController.getUser();

    if (user.name != null &&
        user.name!.isNotEmpty &&
        user.email != null &&
        user.email!.isNotEmpty &&
        user.country != null &&
        user.country!.isNotEmpty &&
        user.mobileNumber != null &&
        user.mobileNumber!.isNotEmpty) {
      return true;
    } else {
      return false;
    }
  }
}
