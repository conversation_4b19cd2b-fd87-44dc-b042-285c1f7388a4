/// Auth Feature Barrel File
/// Exports all public interfaces for the auth feature

// Domain Layer
export 'domain/entities/user.dart';
export 'domain/entities/auth_result.dart';
export 'domain/entities/auth_failure.dart';
export 'domain/repositories/auth_repository.dart';
export 'domain/usecases/login_usecase.dart';
export 'domain/usecases/register_usecase.dart';
export 'domain/usecases/social_login_usecase.dart';
export 'domain/usecases/forgot_password_usecase.dart';
export 'domain/usecases/verify_email_usecase.dart';

// Presentation Layer
export 'presentation/controllers/auth_state_controller.dart';
export 'presentation/controllers/login_controller.dart';

// Dependency Injection
export 'auth_injection.dart';
