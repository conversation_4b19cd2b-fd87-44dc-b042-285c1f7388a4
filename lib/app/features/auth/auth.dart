/// Auth Feature Barrel File
/// Exports all public interfaces for the auth feature

// Domain Layer
export 'domain/entities/user.dart';
export 'domain/entities/auth_result.dart';
export 'domain/entities/auth_failure.dart';
export 'domain/repositories/auth_repository.dart';
export 'domain/usecases/login_usecase.dart';
export 'domain/usecases/register_usecase.dart';
export 'domain/usecases/social_login_usecase.dart';
export 'domain/usecases/forgot_password_usecase.dart';
export 'domain/usecases/verify_email_usecase.dart';

// Presentation Layer - Controllers
export 'presentation/controllers/auth_state_controller.dart';
export 'presentation/controllers/login_controller.dart';
export 'presentation/controllers/register_controller.dart';
export 'presentation/controllers/forgot_password_controller.dart';
export 'presentation/controllers/verify_email_controller.dart';
export 'presentation/controllers/guest_controller.dart';
export 'presentation/controllers/social_form_controller.dart';

// Presentation Layer - Pages
export 'presentation/pages/login_page_clean.dart';
export 'presentation/pages/register/register_page_clean.dart';
export 'presentation/pages/forgot_password/forgot_password_email_page_clean.dart';
export 'presentation/pages/verify_email/verify_email_page_clean.dart';
export 'presentation/pages/guest/guest_page_clean.dart';
export 'presentation/pages/social_form/social_form_page_clean.dart';

// Dependency Injection
export 'auth_injection.dart';
