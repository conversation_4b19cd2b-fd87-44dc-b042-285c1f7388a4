import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../constants/app_color.dart';
import '../../../../../constants/app_config.dart';
import '../../../../../constants/app_text_styles.dart';
import '../../../../../routes/route_names.dart';
import '../../../../../services/navigation_service.dart';
import '../../../../../modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../controllers/verify_email_controller.dart';

/// Clean Architecture Verify Email Page using GetX for state management
class VerifyEmailPageClean extends StatefulWidget {
  const VerifyEmailPageClean({super.key});

  @override
  State<VerifyEmailPageClean> createState() => _VerifyEmailPageCleanState();
}

class _VerifyEmailPageCleanState extends State<VerifyEmailPageClean> {
  late final VerifyEmailController controller;
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    controller = Get.find<VerifyEmailController>();
    navigationService = NavigationService.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => navigationService.back(),
        ),
        title: const Text(
          'Verify Email',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Check Your Email',
              style: AppTextStyles.bigHeaderText,
            ),
            const SizedBox(height: 16),
            Text(
              'We\'ve sent a verification code to ${controller.displayEmail}',
              style: AppTextStyles.normalText,
            ),
            const SizedBox(height: 8),
            const Text(
              'Enter the 6-digit code below to verify your email address.',
              style: AppTextStyles.normalText,
            ),
            const SizedBox(height: 32),
            Form(
              key: controller.formKey,
              child: Column(
                children: [
                  TextFormField(
                    controller: controller.codeController,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 8,
                    ),
                    decoration: const InputDecoration(
                      labelText: 'Verification Code',
                      hintText: '000000',
                      prefixIcon: Icon(Icons.verified_outlined),
                    ),
                    validator: (value) {
                      if (value?.isEmpty ?? true) return 'Code is required';
                      if (value!.length != 6) return 'Code must be 6 digits';
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),
                  Obx(() => SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColor.primaryButtonColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          onPressed: controller.isLoading.value ? null : _handleVerifyEmail,
                          child: controller.isLoading.value
                              ? const CircularProgressIndicator(color: Colors.white)
                              : const Text(
                                  'Verify Email',
                                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                ),
                        ),
                      )),
                ],
              ),
            ),
            const SizedBox(height: 24),
            // Error message
            Obx(() => controller.errorMessage.value.isNotEmpty
                ? Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red.shade600),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            controller.errorMessage.value,
                            style: TextStyle(color: Colors.red.shade600),
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink()),
            const SizedBox(height: 24),
            Center(
              child: Column(
                children: [
                  const Text(
                    'Didn\'t receive the code?',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () {
                      // TODO: Implement resend code functionality
                      Get.snackbar(
                        'Code Sent',
                        'A new verification code has been sent to your email',
                        backgroundColor: Colors.green.shade100,
                        colorText: Colors.green.shade800,
                      );
                    },
                    child: const Text(
                      'Resend Code',
                      style: TextStyle(
                        color: AppColor.primaryButtonColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            Center(
              child: TextButton(
                onPressed: () => navigationService.back(),
                child: const Text(
                  'Back to Registration',
                  style: TextStyle(
                    color: AppColor.primaryButtonColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleVerifyEmail() async {
    controller.clearMessages();
    
    if (await controller.verifyEmail()) {
      // Show success dialog and navigate to main app
      Get.dialog(
        KMessageDialogView(
          content: 'Email verified successfully! Welcome to Automoment!',
          callback: () {
            Get.back();
            navigationService.offAllNamed(RouteNames.bottombar);
          },
        ),
        barrierDismissible: false,
      );
    }
  }
}
