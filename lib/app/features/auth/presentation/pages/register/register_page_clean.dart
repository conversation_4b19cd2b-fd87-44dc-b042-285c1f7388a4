import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:get/get.dart';
import 'package:email_validator/email_validator.dart';
import 'package:country_picker/country_picker.dart';
import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';

import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../constants/app_color.dart';
import '../../../../../constants/app_config.dart';
import '../../../../../constants/app_text_styles.dart';
import '../../../../../routes/route_names.dart';
import '../../../../../services/navigation_service.dart';
import '../../../../../modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../controllers/register_controller.dart';

/// Clean Architecture Register Page using GetX for state management
class RegisterPageClean extends StatefulWidget {
  const RegisterPageClean({super.key});

  @override
  State<RegisterPageClean> createState() => _RegisterPageCleanState();
}

class _RegisterPageCleanState extends State<RegisterPageClean> {
  late final RegisterController controller;
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    controller = Get.find<RegisterController>();
    navigationService = NavigationService.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SafeArea(
        top: false,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  offset: Offset(0, -5),
                  blurRadius: 15,
                  color: Colors.black12,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                _buildRegisterForm(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            children: [
              const Spacer(),
              IconButton(
                onPressed: () => navigationService.back(),
                icon: const Icon(Icons.close),
                iconSize: 30,
                color: Colors.black,
              ),
            ],
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: AppConfig.defaultPadding),
            child: Align(
              alignment: Alignment.topLeft,
              child: Text(
                "Create Account",
                style: AppTextStyles.bigHeaderText,
              ),
            ),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: AppConfig.defaultPadding),
            child: Align(
              alignment: Alignment.topLeft,
              child: Text(
                "Join us and enjoy exclusive deals and experiences!",
                style: AppTextStyles.normalText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRegisterForm() {
    return Padding(
      padding: const EdgeInsets.only(
        left: AppConfig.defaultPadding * 2,
        right: AppConfig.defaultPadding * 2,
        bottom: AppConfig.defaultPadding * 2,
      ),
      child: Form(
        key: controller.formKey,
        child: Column(
          children: [
            // Name field
            TextFormField(
              controller: controller.nameController,
              decoration: const InputDecoration(
                labelText: "Full Name",
                hintText: "Enter your full name",
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) return 'Required';
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Email field
            TextFormField(
              controller: controller.emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                labelText: "Email",
                hintText: "Enter your email",
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) return 'Required';
                if (!EmailValidator.validate(value!)) {
                  return "Invalid email format";
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Password field
            Obx(() => TextFormField(
                  controller: controller.passwordController,
                  obscureText: controller.isPasswordHidden.value,
                  decoration: InputDecoration(
                    labelText: "Password",
                    hintText: "Enter your password",
                    suffixIcon: IconButton(
                      icon: Icon(
                        controller.isPasswordHidden.value
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: controller.togglePasswordVisibility,
                    ),
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? true) return 'Required';
                    if (value!.length < 6) {
                      return 'Password must be at least 6 characters';
                    }
                    return null;
                  },
                )),

            const SizedBox(height: 16),

            // Confirm Password field
            Obx(() => TextFormField(
                  controller: controller.passwordConfirmController,
                  obscureText: controller.isConfirmPasswordHidden.value,
                  decoration: InputDecoration(
                    labelText: "Confirm Password",
                    hintText: "Confirm your password",
                    suffixIcon: IconButton(
                      icon: Icon(
                        controller.isConfirmPasswordHidden.value
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: controller.toggleConfirmPasswordVisibility,
                    ),
                  ),
                  validator: controller.validatePasswordConfirmation,
                )),

            const SizedBox(height: 16),

            // Address field
            TextFormField(
              controller: controller.addressController,
              decoration: const InputDecoration(
                labelText: "Address",
                hintText: "Enter your address",
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) return 'Required';
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Country field
            Obx(() => TextFormField(
                  controller: controller.countryController.value,
                  readOnly: true,
                  decoration: const InputDecoration(
                    labelText: "Country",
                    hintText: "Select your country",
                    suffixIcon: Icon(Icons.arrow_drop_down),
                  ),
                  onTap: () => _showCountryPicker(),
                  validator: (value) {
                    if (value?.isEmpty ?? true) return 'Required';
                    return null;
                  },
                )),

            const SizedBox(height: 16),

            // Mobile number field
            InternationalPhoneNumberInput(
              onInputChanged: controller.setMobileNumber,
              selectorConfig: const SelectorConfig(
                selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
              ),
              ignoreBlank: false,
              autoValidateMode: AutovalidateMode.disabled,
              selectorTextStyle: const TextStyle(color: Colors.black),
              initialValue: controller.mobileNumber,
              textFieldController: controller.mobileNumberController,
              formatInput: false,
              keyboardType: const TextInputType.numberWithOptions(
                signed: true,
                decimal: true,
              ),
              inputDecoration: const InputDecoration(
                labelText: "Mobile Number",
                hintText: "Enter your mobile number",
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) return 'Required';
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Date of birth field
            Obx(() => TextFormField(
                  controller: controller.dateOfBirthController.value,
                  readOnly: true,
                  decoration: const InputDecoration(
                    labelText: "Date of Birth",
                    hintText: "Select your date of birth",
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  onTap: () => _showDatePicker(),
                )),

            const SizedBox(height: 20),

            // Terms and Conditions Checkbox
            _buildTermsCheckbox(),

            const SizedBox(height: 30),

            // Register Button
            _buildRegisterButton(),

            const SizedBox(height: 10),

            // Google Sign Up Button
            _buildGoogleSignUpButton(),

            const SizedBox(height: 10),

            // Apple Sign Up Button (iOS only)
            if (Platform.isIOS) _buildAppleSignUpButton(),

            // Error message
            Obx(() => controller.errorMessage.value.isNotEmpty
                ? Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Text(
                      controller.errorMessage.value,
                      style: const TextStyle(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                  )
                : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  Widget _buildTermsCheckbox() {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Row(
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: Obx(() => Checkbox(
                  checkColor: Colors.white,
                  activeColor: AppColor.secondaryColor,
                  shape: const CircleBorder(),
                  value: controller.isChecked.value,
                  onChanged: (_) => controller.toggleTermsAcceptance(),
                )),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: const TextStyle(color: Colors.black, fontSize: 14),
                children: [
                  const TextSpan(text: "I have read and agree to "),
                  TextSpan(
                    text: "Automoment's Data Protection Terms",
                    style: const TextStyle(
                      color: AppColor.primaryButtonColor,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () => _launchTermsUrl(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRegisterButton() {
    return Obx(() => ElevatedButton(
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.white),
            backgroundColor: WidgetStateProperty.all(
              AppColor.primaryButtonColor,
            ),
            shape: WidgetStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0),
                side: const BorderSide(color: AppColor.primaryButtonColor),
              ),
            ),
          ),
          onPressed: controller.isLoading.value ? null : _handleRegister,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            height: 50,
            child: Center(
              child: controller.isLoading.value
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text(
                      "Sign Up",
                      style: TextStyle(fontSize: 16),
                    ),
            ),
          ),
        ));
  }

  Widget _buildGoogleSignUpButton() {
    return Obx(() => ElevatedButton(
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.white),
            backgroundColor: WidgetStateProperty.all(
              AppColor.primaryButtonColor,
            ),
            shape: WidgetStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0),
                side: const BorderSide(color: AppColor.primaryButtonColor),
              ),
            ),
          ),
          onPressed: controller.isLoading.value ? null : _handleGoogleSignUp,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            height: 50,
            child: Row(
              children: [
                const Spacer(),
                Image.asset("assets/images/google.png", height: 20),
                const SizedBox(width: 10),
                const Text(
                  "Sign Up with Google",
                  style: TextStyle(fontSize: 16),
                ),
                const Spacer(),
              ],
            ),
          ),
        ));
  }

  Widget _buildAppleSignUpButton() {
    return Obx(() => ElevatedButton(
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.white),
            backgroundColor: WidgetStateProperty.all(
              AppColor.primaryButtonColor,
            ),
            shape: WidgetStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0),
                side: const BorderSide(color: AppColor.primaryButtonColor),
              ),
            ),
          ),
          onPressed: controller.isLoading.value ? null : _handleAppleSignUp,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            height: 50,
            child: Row(
              children: [
                const Spacer(),
                Image.asset("assets/images/apple.png", height: 20),
                const SizedBox(width: 10),
                const Text(
                  "Sign Up with Apple",
                  style: TextStyle(fontSize: 16),
                ),
                const Spacer(),
              ],
            ),
          ),
        ));
  }

  void _showCountryPicker() {
    showCountryPicker(
      context: context,
      onSelect: (Country country) {
        controller.setCountry(country.name);
      },
    );
  }

  void _showDatePicker() {
    DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: DateTime(1900, 1, 1),
      maxTime: DateTime.now(),
      onConfirm: (date) {
        controller.setBirthDate(date);
      },
      currentTime: controller.birthDate ?? DateTime.now(),
      locale: LocaleType.en,
    );
  }

  void _launchTermsUrl() async {
    final uri = Uri.parse('https://automoment.com/terms');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  Future<void> _handleRegister() async {
    controller.clearError();

    if (await controller.register()) {
      final param = {
        "email": controller.emailController.text,
        "deviceName": controller.deviceName,
      };
      navigationService.toNamed(RouteNames.verifyEmail, arguments: param);
    } else if (controller.errorMessage.value.isNotEmpty) {
      Get.dialog(
        KMessageDialogView(content: controller.errorMessage.value),
        barrierDismissible: false,
      );
    }
  }

  Future<void> _handleGoogleSignUp() async {
    controller.clearError();

    if (await controller.signInWithGoogle()) {
      _handleSocialLogin("Google");
    } else if (controller.errorMessage.value.isNotEmpty) {
      Get.dialog(
        KMessageDialogView(content: controller.errorMessage.value),
        barrierDismissible: false,
      );
    }
  }

  Future<void> _handleAppleSignUp() async {
    controller.clearError();

    if (await controller.signInWithApple()) {
      _handleSocialLogin("Apple");
    } else if (controller.errorMessage.value.isNotEmpty) {
      Get.dialog(
        KMessageDialogView(content: controller.errorMessage.value),
        barrierDismissible: false,
      );
    }
  }

  void _handleSocialLogin(String provider) {
    if (controller.isAllRequiredUserFieldsFilled()) {
      navigationService.offAllNamed(RouteNames.bottombar);
    } else {
      navigationService.offAllNamed(
        RouteNames.socialLoginForm,
        arguments: provider,
      );
    }
  }
}
