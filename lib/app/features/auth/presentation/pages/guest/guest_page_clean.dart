import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../constants/app_color.dart';
import '../../../../../constants/app_config.dart';
import '../../../../../constants/app_text_styles.dart';
import '../../../../../routes/route_names.dart';
import '../../../../../services/navigation_service.dart';
import '../../controllers/guest_controller.dart';

/// Clean Architecture Guest/Welcome Page using GetX for state management
class GuestPageClean extends StatefulWidget {
  const GuestPageClean({super.key});

  @override
  State<GuestPageClean> createState() => _GuestPageCleanState();
}

class _GuestPageCleanState extends State<GuestPageClean> {
  late final GuestController controller;
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    controller = Get.find<GuestController>();
    navigationService = NavigationService.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
          child: Column(
            children: [
              const Spacer(),
              // Logo or App Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColor.primaryButtonColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.directions_car,
                  size: 60,
                  color: AppColor.primaryButtonColor,
                ),
              ),
              const SizedBox(height: 32),
              // Welcome Text
              const Text(
                'Welcome to Automoment',
                style: AppTextStyles.bigHeaderText,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'Discover amazing automotive deals and experiences. Join our community today!',
                style: AppTextStyles.normalText,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              // Action Buttons
              Column(
                children: [
                  // Sign Up Button
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColor.primaryButtonColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onPressed: () => _navigateToRegister(),
                      child: const Text(
                        'Create Account',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Sign In Button
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColor.primaryButtonColor,
                        side: const BorderSide(color: AppColor.primaryButtonColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onPressed: () => _navigateToLogin(),
                      child: const Text(
                        'Sign In',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Continue as Guest
                  TextButton(
                    onPressed: () => _continueAsGuest(),
                    child: const Text(
                      'Continue as Guest',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              // Terms and Privacy
              Wrap(
                alignment: WrapAlignment.center,
                children: [
                  const Text(
                    'By continuing, you agree to our ',
                    style: TextStyle(color: Colors.grey, fontSize: 12),
                  ),
                  GestureDetector(
                    onTap: () => _showTerms(),
                    child: const Text(
                      'Terms of Service',
                      style: TextStyle(
                        color: AppColor.primaryButtonColor,
                        fontSize: 12,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                  const Text(
                    ' and ',
                    style: TextStyle(color: Colors.grey, fontSize: 12),
                  ),
                  GestureDetector(
                    onTap: () => _showPrivacyPolicy(),
                    child: const Text(
                      'Privacy Policy',
                      style: TextStyle(
                        color: AppColor.primaryButtonColor,
                        fontSize: 12,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToRegister() {
    navigationService.toNamed(RouteNames.register);
  }

  void _navigateToLogin() {
    navigationService.toNamed(RouteNames.login);
  }

  void _continueAsGuest() {
    // Navigate to main app without authentication
    navigationService.offAllNamed(RouteNames.bottombar);
  }

  void _showTerms() {
    // TODO: Show terms of service
    Get.snackbar(
      'Terms of Service',
      'Terms of Service would be displayed here',
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }

  void _showPrivacyPolicy() {
    // TODO: Show privacy policy
    Get.snackbar(
      'Privacy Policy',
      'Privacy Policy would be displayed here',
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }
}
