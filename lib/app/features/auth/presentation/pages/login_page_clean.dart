import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:email_validator/email_validator.dart';

import '../../../../constants/app_color.dart';
import '../../../../constants/app_config.dart';
import '../../../../constants/app_text_styles.dart';
import '../../../../routes/route_names.dart';
import '../../../../services/navigation_service.dart';
import '../../../../modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../controllers/login_controller.dart';

/// Clean Architecture Login Page using GetX for state management
class LoginPageClean extends StatefulWidget {
  const LoginPageClean({super.key});

  @override
  State<LoginPageClean> createState() => _LoginPageCleanState();
}

class _LoginPageCleanState extends State<LoginPageClean> {
  late final LoginController controller;
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    controller = Get.find<LoginController>();
    navigationService = NavigationService.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SafeArea(
        top: false,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  offset: Offset(0, -5),
                  blurRadius: 15,
                  color: Colors.black12,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                _buildLoginForm(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            children: [
              const Spacer(),
              IconButton(
                onPressed: () => navigationService.back(),
                icon: const Icon(Icons.close),
                iconSize: 30,
                color: Colors.black,
              ),
            ],
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: AppConfig.defaultPadding),
            child: Align(
              alignment: Alignment.topLeft,
              child: Text(
                "Welcome Back!",
                style: AppTextStyles.bigHeaderText,
              ),
            ),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: AppConfig.defaultPadding),
            child: Align(
              alignment: Alignment.topLeft,
              child: Text(
                "Login now and check out the latest deals available!",
                style: AppTextStyles.normalText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginForm() {
    return Padding(
      padding: const EdgeInsets.only(
        left: AppConfig.defaultPadding * 2,
        right: AppConfig.defaultPadding * 2,
        bottom: AppConfig.defaultPadding * 2,
      ),
      child: Form(
        key: controller.formKey,
        child: Column(
          children: [
            // Email field
            TextFormField(
              controller: controller.emailController,
              keyboardType: TextInputType.emailAddress,
              autocorrect: false,
              decoration: const InputDecoration(
                labelText: "Email",
                hintText: "",
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) return 'Required';
                if (!EmailValidator.validate(value!)) {
                  return "Invalid email pattern";
                }
                return null;
              },
            ),

            // Password field
            Obx(() => TextFormField(
                  controller: controller.passwordController,
                  obscureText: controller.isPasswordHidden.value,
                  decoration: InputDecoration(
                    labelText: "Password",
                    suffixIcon: IconButton(
                      icon: Icon(
                        controller.isPasswordHidden.value
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: controller.togglePasswordVisibility,
                    ),
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? true) return 'Required';
                    return null;
                  },
                )),

            // Forgot password link
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: AppConfig.defaultPadding * 2,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    child: const Text("Forgot password?"),
                    onTap: () => navigationService.toNamed(
                      RouteNames.forgotPasswordEmail,
                    ),
                  ),
                ],
              ),
            ),

            // Remember me checkbox
            Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Row(
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: Obx(() => Checkbox(
                          activeColor: AppColor.secondaryColor,
                          value: controller.isChecked.value,
                          onChanged: (_) => controller.toggleRememberMe(),
                        )),
                  ),
                  const SizedBox(width: 10),
                  const Text("Remember me"),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Login button
            Obx(() => ElevatedButton(
                  style: ButtonStyle(
                    foregroundColor: WidgetStateProperty.all(Colors.white),
                    backgroundColor: WidgetStateProperty.all(
                      AppColor.primaryButtonColor,
                    ),
                    shape: WidgetStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.0),
                        side: const BorderSide(
                          color: AppColor.primaryButtonColor,
                        ),
                      ),
                    ),
                  ),
                  onPressed: controller.isLoading.value ? null : _handleLogin,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    height: 50,
                    child: Center(
                      child: controller.isLoading.value
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text(
                              "Sign In",
                              style: TextStyle(fontSize: 16),
                            ),
                    ),
                  ),
                )),

            const SizedBox(height: 10),

            // Google Sign In Button
            _buildGoogleSignInButton(),

            const SizedBox(height: 10),

            // Apple Sign In Button (iOS only)
            if (Platform.isIOS) _buildAppleSignInButton(),

            // Error message
            Obx(() => controller.errorMessage.value.isNotEmpty
                ? Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Text(
                      controller.errorMessage.value,
                      style: const TextStyle(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                  )
                : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  Widget _buildGoogleSignInButton() {
    return Obx(() => ElevatedButton(
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.white),
            backgroundColor: WidgetStateProperty.all(
              AppColor.primaryButtonColor,
            ),
            shape: WidgetStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0),
                side: const BorderSide(color: AppColor.primaryButtonColor),
              ),
            ),
          ),
          onPressed: controller.isLoading.value ? null : _handleGoogleLogin,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            height: 50,
            child: Row(
              children: [
                const Spacer(),
                Image.asset("assets/images/google.png", height: 20),
                const SizedBox(width: 10),
                const Text(
                  "Sign In with Google",
                  style: TextStyle(fontSize: 16),
                ),
                const Spacer(),
              ],
            ),
          ),
        ));
  }

  Widget _buildAppleSignInButton() {
    return Obx(() => ElevatedButton(
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.white),
            backgroundColor: WidgetStateProperty.all(
              AppColor.primaryButtonColor,
            ),
            shape: WidgetStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0),
                side: const BorderSide(color: AppColor.primaryButtonColor),
              ),
            ),
          ),
          onPressed: controller.isLoading.value ? null : _handleAppleLogin,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            height: 50,
            child: Row(
              children: [
                const Spacer(),
                Image.asset("assets/images/apple.png", height: 20),
                const SizedBox(width: 10),
                const Text(
                  "Sign In with Apple",
                  style: TextStyle(fontSize: 16),
                ),
                const Spacer(),
              ],
            ),
          ),
        ));
  }

  Future<void> _handleLogin() async {
    controller.clearError();

    if (await controller.login()) {
      _handleSuccessfulLogin();
    } else if (controller.errorMessage.value.isNotEmpty) {
      Get.dialog(
        KMessageDialogView(content: controller.errorMessage.value),
        barrierDismissible: false,
      );
    }
  }

  Future<void> _handleGoogleLogin() async {
    controller.clearError();

    if (await controller.signInWithGoogle()) {
      _handleSocialLogin("Google");
    } else if (controller.errorMessage.value.isNotEmpty) {
      Get.dialog(
        KMessageDialogView(content: controller.errorMessage.value),
        barrierDismissible: false,
      );
    }
  }

  Future<void> _handleAppleLogin() async {
    controller.clearError();

    if (await controller.signInWithApple()) {
      _handleSocialLogin("Apple");
    } else if (controller.errorMessage.value.isNotEmpty) {
      Get.dialog(
        KMessageDialogView(content: controller.errorMessage.value),
        barrierDismissible: false,
      );
    }
  }

  void _handleSuccessfulLogin() {
    navigationService.offAllNamed(RouteNames.bottombar);
  }

  void _handleSocialLogin(String provider) {
    if (controller.isAllRequiredUserFieldsFilled()) {
      _handleSuccessfulLogin();
    } else {
      navigationService.offAllNamed(
        RouteNames.socialLoginForm,
        arguments: provider,
      );
    }
  }
}
