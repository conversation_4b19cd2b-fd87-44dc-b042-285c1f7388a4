import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:country_picker/country_picker.dart';
import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

import '../../../../../constants/app_color.dart';
import '../../../../../constants/app_config.dart';
import '../../../../../constants/app_text_styles.dart';
import '../../../../../routes/route_names.dart';
import '../../../../../services/navigation_service.dart';
import '../../../../../modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../controllers/social_form_controller.dart';

/// Clean Architecture Social Form Page using GetX for state management
class SocialFormPageClean extends StatefulWidget {
  const SocialFormPageClean({super.key});

  @override
  State<SocialFormPageClean> createState() => _SocialFormPageCleanState();
}

class _SocialFormPageCleanState extends State<SocialFormPageClean> {
  late final SocialFormController controller;
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    controller = Get.find<SocialFormController>();
    navigationService = NavigationService.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => navigationService.back(),
        ),
        title: const Text(
          'Complete Profile',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Obx(() => Text(
                  controller.welcomeMessage,
                  style: AppTextStyles.bigHeaderText,
                )),
            const SizedBox(height: 8),
            const Text(
              'Please provide the following information to complete your registration.',
              style: AppTextStyles.normalText,
            ),
            const SizedBox(height: 32),
            // Form
            Form(
              key: controller.formKey,
              child: Column(
                children: [
                  // Name field
                  TextFormField(
                    controller: controller.nameController,
                    decoration: const InputDecoration(
                      labelText: 'Full Name',
                      hintText: 'Enter your full name',
                      prefixIcon: Icon(Icons.person_outline),
                    ),
                    validator: (value) {
                      if (value?.isEmpty ?? true) return 'Name is required';
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  // Address field
                  TextFormField(
                    controller: controller.addressController,
                    decoration: const InputDecoration(
                      labelText: 'Address',
                      hintText: 'Enter your address',
                      prefixIcon: Icon(Icons.location_on_outlined),
                    ),
                    validator: (value) {
                      if (value?.isEmpty ?? true) return 'Address is required';
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  // Country field
                  Obx(() => TextFormField(
                        controller: controller.countryController.value,
                        readOnly: true,
                        decoration: const InputDecoration(
                          labelText: 'Country',
                          hintText: 'Select your country',
                          prefixIcon: Icon(Icons.flag_outlined),
                          suffixIcon: Icon(Icons.arrow_drop_down),
                        ),
                        onTap: () => _showCountryPicker(),
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Country is required';
                          return null;
                        },
                      )),
                  const SizedBox(height: 16),
                  // Mobile number field
                  InternationalPhoneNumberInput(
                    onInputChanged: controller.setMobileNumber,
                    selectorConfig: const SelectorConfig(
                      selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                    ),
                    ignoreBlank: false,
                    autoValidateMode: AutovalidateMode.disabled,
                    selectorTextStyle: const TextStyle(color: Colors.black),
                    initialValue: controller.mobileNumber,
                    textFieldController: controller.mobileNumberController,
                    formatInput: false,
                    keyboardType: const TextInputType.numberWithOptions(
                      signed: true,
                      decimal: true,
                    ),
                    inputDecoration: const InputDecoration(
                      labelText: 'Mobile Number',
                      hintText: 'Enter your mobile number',
                      prefixIcon: Icon(Icons.phone_outlined),
                    ),
                    validator: (value) {
                      if (value?.isEmpty ?? true) return 'Mobile number is required';
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  // Date of birth field
                  Obx(() => TextFormField(
                        controller: controller.dateOfBirthController.value,
                        readOnly: true,
                        decoration: const InputDecoration(
                          labelText: 'Date of Birth',
                          hintText: 'Select your date of birth',
                          prefixIcon: Icon(Icons.calendar_today_outlined),
                          suffixIcon: Icon(Icons.arrow_drop_down),
                        ),
                        onTap: () => _showDatePicker(),
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Date of birth is required';
                          return null;
                        },
                      )),
                  const SizedBox(height: 32),
                  // Complete Profile Button
                  Obx(() => SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColor.primaryButtonColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          onPressed: controller.isLoading.value ? null : _handleCompleteProfile,
                          child: controller.isLoading.value
                              ? const CircularProgressIndicator(color: Colors.white)
                              : const Text(
                                  'Complete Profile',
                                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                ),
                        ),
                      )),
                ],
              ),
            ),
            const SizedBox(height: 24),
            // Error message
            Obx(() => controller.errorMessage.value.isNotEmpty
                ? Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red.shade600),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            controller.errorMessage.value,
                            style: TextStyle(color: Colors.red.shade600),
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink()),
            const SizedBox(height: 24),
            // Skip for now option
            Center(
              child: TextButton(
                onPressed: () => _skipForNow(),
                child: const Text(
                  'Skip for now',
                  style: TextStyle(
                    color: Colors.grey,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCountryPicker() {
    showCountryPicker(
      context: context,
      onSelect: (Country country) {
        controller.setCountry(country.name);
      },
    );
  }

  void _showDatePicker() {
    DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: DateTime(1900, 1, 1),
      maxTime: DateTime.now(),
      onConfirm: (date) {
        controller.setBirthDate(date);
      },
      currentTime: controller.birthDate ?? DateTime.now(),
      locale: LocaleType.en,
    );
  }

  Future<void> _handleCompleteProfile() async {
    controller.clearError();
    
    if (await controller.completeProfile()) {
      // Show success dialog and navigate to main app
      Get.dialog(
        KMessageDialogView(
          content: 'Profile completed successfully! Welcome to Automoment!',
          callback: () {
            Get.back();
            navigationService.offAllNamed(RouteNames.bottombar);
          },
        ),
        barrierDismissible: false,
      );
    } else if (controller.errorMessage.value.isNotEmpty) {
      Get.dialog(
        KMessageDialogView(content: controller.errorMessage.value),
        barrierDismissible: false,
      );
    }
  }

  void _skipForNow() {
    // Navigate to main app without completing profile
    navigationService.offAllNamed(RouteNames.bottombar);
  }
}
