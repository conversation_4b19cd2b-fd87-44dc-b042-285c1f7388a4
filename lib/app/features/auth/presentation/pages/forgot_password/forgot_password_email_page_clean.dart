import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:email_validator/email_validator.dart';

import '../../../../../constants/app_color.dart';
import '../../../../../constants/app_config.dart';
import '../../../../../constants/app_text_styles.dart';
import '../../../../../routes/route_names.dart';
import '../../../../../services/navigation_service.dart';
import '../../../../../modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../controllers/forgot_password_controller.dart';

/// Clean Architecture Forgot Password Email Page using GetX for state management
class ForgotPasswordEmailPageClean extends StatefulWidget {
  const ForgotPasswordEmailPageClean({super.key});

  @override
  State<ForgotPasswordEmailPageClean> createState() => _ForgotPasswordEmailPageCleanState();
}

class _ForgotPasswordEmailPageCleanState extends State<ForgotPasswordEmailPageClean> {
  late final ForgotPasswordController controller;
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    controller = Get.find<ForgotPasswordController>();
    navigationService = NavigationService.instance;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => navigationService.back(),
        ),
        title: const Text(
          'Reset Password',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Forgot Your Password?',
              style: AppTextStyles.bigHeaderText,
            ),
            const SizedBox(height: 16),
            const Text(
              'Enter your email address and we\'ll send you a link to reset your password.',
              style: AppTextStyles.normalText,
            ),
            const SizedBox(height: 32),
            Form(
              key: controller.formKey,
              child: Column(
                children: [
                  TextFormField(
                    controller: controller.emailController,
                    keyboardType: TextInputType.emailAddress,
                    decoration: const InputDecoration(
                      labelText: 'Email Address',
                      hintText: 'Enter your email',
                      prefixIcon: Icon(Icons.email_outlined),
                    ),
                    validator: (value) {
                      if (value?.isEmpty ?? true) return 'Email is required';
                      if (!EmailValidator.validate(value!)) {
                        return 'Please enter a valid email';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),
                  Obx(() => SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColor.primaryButtonColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          onPressed: controller.isLoading.value ? null : _handleSendResetEmail,
                          child: controller.isLoading.value
                              ? const CircularProgressIndicator(color: Colors.white)
                              : const Text(
                                  'Send Reset Link',
                                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                ),
                        ),
                      )),
                ],
              ),
            ),
            const SizedBox(height: 24),
            // Error message
            Obx(() => controller.errorMessage.value.isNotEmpty
                ? Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red.shade600),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            controller.errorMessage.value,
                            style: TextStyle(color: Colors.red.shade600),
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink()),
            // Success message
            Obx(() => controller.successMessage.value.isNotEmpty
                ? Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle_outline, color: Colors.green.shade600),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            controller.successMessage.value,
                            style: TextStyle(color: Colors.green.shade600),
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink()),
            const Spacer(),
            Center(
              child: TextButton(
                onPressed: () => navigationService.back(),
                child: const Text(
                  'Back to Login',
                  style: TextStyle(
                    color: AppColor.primaryButtonColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSendResetEmail() async {
    controller.clearMessages();
    
    if (await controller.sendResetEmail()) {
      // Show success dialog and navigate to check code page
      Get.dialog(
        KMessageDialogView(
          content: controller.successMessage.value,
          callback: () {
            Get.back();
            navigationService.toNamed(RouteNames.forgotPasswordCheckCode);
          },
        ),
        barrierDismissible: false,
      );
    }
  }
}
