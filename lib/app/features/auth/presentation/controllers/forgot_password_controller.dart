import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../controllers/base_screen_controller.dart';
import '../../domain/usecases/forgot_password_usecase.dart';

/// Clean Architecture Forgot Password Controller using GetX
class ForgotPasswordController extends BaseScreenController {
  final ForgotPasswordUseCase _forgotPasswordUseCase;

  ForgotPasswordController(this._forgotPasswordUseCase);

  @override
  String get screenName => 'Forgot Password';

  // UI state
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();

  @override
  void onClose() {
    emailController.dispose();
    super.onClose();
  }

  /// Send password reset email
  Future<bool> sendResetEmail() async {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    isLoading.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    try {
      final result = await _forgotPasswordUseCase.sendResetEmail(
        email: emailController.text.trim(),
      );

      if (result is Success) {
        successMessage.value = 'Password reset email sent successfully';
        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'An unexpected error occurred';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Clear messages
  void clearMessages() {
    errorMessage.value = '';
    successMessage.value = '';
  }
}

/// Controller for password reset code verification
class ForgotPasswordCheckCodeController extends BaseScreenController {
  final ForgotPasswordUseCase _forgotPasswordUseCase;

  ForgotPasswordCheckCodeController(this._forgotPasswordUseCase);

  @override
  String get screenName => 'Verify Reset Code';

  // UI state
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final codeController = TextEditingController();

  @override
  void onClose() {
    codeController.dispose();
    super.onClose();
  }

  /// Verify password reset code
  Future<bool> verifyResetCode() async {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    isLoading.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    try {
      final result = await _forgotPasswordUseCase.verifyResetCode(
        code: codeController.text.trim(),
      );

      if (result is Success) {
        successMessage.value = 'Code verified successfully';
        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'An unexpected error occurred';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Clear messages
  void clearMessages() {
    errorMessage.value = '';
    successMessage.value = '';
  }
}

/// Controller for password reset
class ForgotPasswordResetController extends BaseScreenController {
  final ForgotPasswordUseCase _forgotPasswordUseCase;

  ForgotPasswordResetController(this._forgotPasswordUseCase);

  @override
  String get screenName => 'Reset Password';

  // UI state
  final RxBool isPasswordHidden = true.obs;
  final RxBool isConfirmPasswordHidden = true.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final codeController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  @override
  void onClose() {
    codeController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }

  /// Reset password with code
  Future<bool> resetPassword() async {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    isLoading.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    try {
      final result = await _forgotPasswordUseCase.resetPassword(
        code: codeController.text.trim(),
        password: passwordController.text,
        passwordConfirmation: confirmPasswordController.text,
      );

      if (result is Success) {
        successMessage.value = 'Password reset successfully';
        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'An unexpected error occurred';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Toggle password visibility
  void togglePasswordVisibility() {
    isPasswordHidden.value = !isPasswordHidden.value;
  }

  /// Toggle confirm password visibility
  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordHidden.value = !isConfirmPasswordHidden.value;
  }

  /// Validate password confirmation
  String? validatePasswordConfirmation(String? value) {
    if (value?.isEmpty ?? true) return 'Required';
    if (value != passwordController.text) {
      return 'Password confirmation does not match';
    }
    return null;
  }

  /// Clear messages
  void clearMessages() {
    errorMessage.value = '';
    successMessage.value = '';
  }
}
