import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

import '../../../../controllers/base_screen_controller.dart';
import '../../../../helpers/device_util.dart';
import '../../domain/usecases/register_usecase.dart';
import '../../domain/usecases/social_login_usecase.dart';
import 'auth_state_controller.dart';

/// Clean Architecture Register Controller using GetX
class RegisterController extends BaseScreenController {
  final RegisterUseCase _registerUseCase;
  final SocialLoginUseCase _socialLoginUseCase;
  final AuthStateController _authStateController;

  RegisterController(
    this._registerUseCase,
    this._socialLoginUseCase,
    this._authStateController,
  );

  @override
  String get screenName => 'Register';

  // UI state
  final RxBool isPasswordHidden = true.obs;
  final RxBool isConfirmPasswordHidden = true.obs;
  final RxBool isChecked = false.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final passwordConfirmController = TextEditingController();
  final addressController = TextEditingController();
  final mobileNumberController = TextEditingController();
  final countryController = TextEditingController().obs;
  final dateOfBirthController = TextEditingController().obs;

  DateTime? birthDate;
  PhoneNumber mobileNumber = PhoneNumber(isoCode: 'SG');
  String? deviceName;

  @override
  Future<void> onInit() async {
    super.onInit();
    deviceName = await DeviceUtil().getDeviceName();
  }

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    passwordConfirmController.dispose();
    addressController.dispose();
    mobileNumberController.dispose();
    countryController.value.dispose();
    dateOfBirthController.value.dispose();
    super.onClose();
  }

  /// Execute user registration
  Future<bool> register() async {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    if (!isChecked.value) {
      errorMessage.value = "You need to read and understand Automoment's Data Protection Terms.";
      return false;
    }

    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _registerUseCase.call(
        name: nameController.text.trim(),
        email: emailController.text.trim(),
        password: passwordController.text,
        passwordConfirmation: passwordConfirmController.text,
        deviceName: deviceName ?? 'Unknown Device',
        address: addressController.text.trim(),
        country: countryController.value.text.trim(),
        mobileNumber: mobileNumberController.text.trim(),
        birthDate: birthDate,
      );

      if (result is Success) {
        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'An unexpected error occurred';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Execute Google registration/login
  Future<bool> signInWithGoogle() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _socialLoginUseCase.loginWithGoogle(
        deviceName: deviceName ?? 'Unknown Device',
      );

      if (result is Success) {
        // Update global auth state
        _authStateController.setUser(result.data.user);
        _authStateController.setToken(result.data.token);
        _authStateController.setIsLoggedIn(true);
        _authStateController.setIsLoggedInWithFirebase(true);
        
        if (result.data.unreadNotificationCount != null) {
          _authStateController.setUnreadNotificationCount(
            result.data.unreadNotificationCount!,
          );
        }

        // Log analytics event
        await analytics.logLogin(method: 'google');
        await analytics.setUserProperties(
          userId: result.data.user.id.toString(),
          country: result.data.user.country,
        );

        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'Google sign-in failed';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Execute Apple registration/login
  Future<bool> signInWithApple() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _socialLoginUseCase.loginWithApple(
        deviceName: deviceName ?? 'Unknown Device',
      );

      if (result is Success) {
        // Update global auth state
        _authStateController.setUser(result.data.user);
        _authStateController.setToken(result.data.token);
        _authStateController.setIsLoggedIn(true);
        _authStateController.setIsLoggedInWithFirebase(true);
        
        if (result.data.unreadNotificationCount != null) {
          _authStateController.setUnreadNotificationCount(
            result.data.unreadNotificationCount!,
          );
        }

        // Log analytics event
        await analytics.logLogin(method: 'apple');
        await analytics.setUserProperties(
          userId: result.data.user.id.toString(),
          country: result.data.user.country,
        );

        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'Apple sign-in failed';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Check if user has all required fields filled
  bool isAllRequiredUserFieldsFilled() {
    return _authStateController.isAllRequiredUserFieldsFilled();
  }

  /// Toggle password visibility
  void togglePasswordVisibility() {
    isPasswordHidden.value = !isPasswordHidden.value;
  }

  /// Toggle confirm password visibility
  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordHidden.value = !isConfirmPasswordHidden.value;
  }

  /// Toggle terms and conditions checkbox
  void toggleTermsAcceptance() {
    isChecked.value = !isChecked.value;
  }

  /// Set birth date
  void setBirthDate(DateTime date) {
    birthDate = date;
    dateOfBirthController.value.text = date.toString().split(' ')[0];
  }

  /// Set country
  void setCountry(String country) {
    countryController.value.text = country;
  }

  /// Set mobile number
  void setMobileNumber(PhoneNumber number) {
    mobileNumber = number;
    mobileNumberController.text = number.phoneNumber ?? '';
  }

  /// Clear error message
  void clearError() {
    errorMessage.value = '';
  }

  /// Validate password confirmation
  String? validatePasswordConfirmation(String? value) {
    if (value?.isEmpty ?? true) return 'Required';
    if (value != passwordController.text) {
      return 'Password confirmation does not match';
    }
    return null;
  }
}
