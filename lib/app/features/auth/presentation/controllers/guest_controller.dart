import 'package:get/get.dart';

import '../../../../controllers/base_screen_controller.dart';

/// Clean Architecture Guest Controller using GetX
/// Handles the guest/welcome screen functionality
class GuestController extends BaseScreenController {
  
  @override
  String get screenName => 'Guest';

  // UI state
  final RxBool isLoading = false.obs;

  @override
  Future<void> onReady() async {
    super.onReady();
    // Any initialization logic for guest screen
  }

  /// Navigate to login
  void navigateToLogin() {
    // This will be handled by the view using NavigationService
    // Controller just manages state, not navigation
  }

  /// Navigate to register
  void navigateToRegister() {
    // This will be handled by the view using NavigationService
    // Controller just manages state, not navigation
  }

  /// Set loading state
  void setLoading(bool loading) {
    isLoading.value = loading;
  }
}
