import 'package:get/get.dart';

import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';

/// Global auth state controller using GetX
/// Manages the overall authentication state of the app
class AuthStateController extends GetxController {
  final AuthRepository _authRepository;

  AuthStateController(this._authRepository);

  // Reactive state variables
  final Rx<User?> _currentUser = Rx<User?>(null);
  final RxString _currentToken = ''.obs;
  final RxBool _isLoggedIn = false.obs;
  final RxInt _unreadNotificationCount = 0.obs;
  final RxBool _isLoggedInWithFirebase = false.obs;

  // Getters for reactive state
  User? get currentUser => _currentUser.value;
  String get currentToken => _currentToken.value;
  bool get isLoggedIn => _isLoggedIn.value;
  int get unreadNotificationCount => _unreadNotificationCount.value;
  bool get isLoggedInWithFirebase => _isLoggedInWithFirebase.value;

  // Reactive getters for UI binding
  Rx<User?> get currentUserRx => _currentUser;
  RxString get currentTokenRx => _currentToken;
  RxBool get isLoggedInRx => _isLoggedIn;
  RxInt get unreadNotificationCountRx => _unreadNotificationCount;
  RxBool get isLoggedInWithFirebaseRx => _isLoggedInWithFirebase;

  @override
  void onInit() {
    super.onInit();
    _loadAuthState();
  }

  /// Load authentication state from local storage
  Future<void> _loadAuthState() async {
    try {
      final user = await _authRepository.getCurrentUser();
      final token = await _authRepository.getCurrentToken();
      final loggedIn = await _authRepository.isLoggedIn();

      _currentUser.value = user;
      _currentToken.value = token ?? '';
      _isLoggedIn.value = loggedIn;
    } catch (e) {
      // Handle error silently, user will need to login again
      _clearAuthState();
    }
  }

  /// Set user data after successful authentication
  void setUser(User user) {
    _currentUser.value = user;
  }

  /// Set authentication token
  void setToken(String token) {
    _currentToken.value = token;
  }

  /// Set login status
  void setIsLoggedIn(bool loggedIn) {
    _isLoggedIn.value = loggedIn;
  }

  /// Set unread notification count
  void setUnreadNotificationCount(int count) {
    _unreadNotificationCount.value = count;
  }

  /// Set Firebase login status
  void setIsLoggedInWithFirebase(bool loggedIn) {
    _isLoggedInWithFirebase.value = loggedIn;
  }

  /// Get current user (non-reactive)
  User getUser() {
    return _currentUser.value ?? const User();
  }

  /// Check if user has all required fields filled
  bool isAllRequiredUserFieldsFilled() {
    return _currentUser.value?.hasRequiredFields ?? false;
  }

  /// Clear all authentication state
  Future<void> clearAuthState() async {
    await _authRepository.clearAuthData();
    _clearAuthState();
  }

  void _clearAuthState() {
    _currentUser.value = null;
    _currentToken.value = '';
    _isLoggedIn.value = false;
    _unreadNotificationCount.value = 0;
    _isLoggedInWithFirebase.value = false;
  }

  /// Update user data
  Future<void> updateUser(User user) async {
    await _authRepository.saveUser(user);
    _currentUser.value = user;
  }

  /// Refresh auth state from repository
  Future<void> refreshAuthState() async {
    await _loadAuthState();
  }
}
