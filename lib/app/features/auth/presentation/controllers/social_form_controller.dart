import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

import '../../../../controllers/base_screen_controller.dart';
import '../../../../helpers/device_util.dart';
import '../../domain/usecases/register_usecase.dart';
import 'auth_state_controller.dart';

/// Clean Architecture Social Form Controller using GetX
/// Handles additional information collection after social login
class SocialFormController extends BaseScreenController {
  final RegisterUseCase _registerUseCase;
  final AuthStateController _authStateController;

  SocialFormController(
    this._registerUseCase,
    this._authStateController,
  );

  @override
  String get screenName => 'Social Login Form';

  // UI state
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString socialProvider = ''.obs;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final nameController = TextEditingController();
  final addressController = TextEditingController();
  final mobileNumberController = TextEditingController();
  final countryController = TextEditingController().obs;
  final dateOfBirthController = TextEditingController().obs;

  DateTime? birthDate;
  PhoneNumber mobileNumber = PhoneNumber(isoCode: 'SG');
  String? deviceName;

  @override
  Future<void> onInit() async {
    super.onInit();
    
    // Get social provider from arguments
    final provider = Get.arguments as String?;
    socialProvider.value = provider ?? 'Social';
    
    deviceName = await DeviceUtil().getDeviceName();
    
    // Pre-fill form with existing user data if available
    _prefillUserData();
  }

  @override
  void onClose() {
    nameController.dispose();
    addressController.dispose();
    mobileNumberController.dispose();
    countryController.value.dispose();
    dateOfBirthController.value.dispose();
    super.onClose();
  }

  /// Pre-fill form with existing user data
  void _prefillUserData() {
    final currentUser = _authStateController.currentUser;
    if (currentUser != null) {
      nameController.text = currentUser.name ?? '';
      addressController.text = currentUser.address ?? '';
      mobileNumberController.text = currentUser.mobileNumber ?? '';
      countryController.value.text = currentUser.country ?? '';
      
      if (currentUser.birthDate != null) {
        birthDate = currentUser.birthDate;
        dateOfBirthController.value.text = 
            currentUser.birthDate.toString().split(' ')[0];
      }
    }
  }

  /// Complete social login by updating user profile
  Future<bool> completeProfile() async {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    isLoading.value = true;
    errorMessage.value = '';

    try {
      final currentUser = _authStateController.currentUser;
      if (currentUser == null) {
        errorMessage.value = 'User session expired. Please login again.';
        return false;
      }

      // Update user with additional information
      final updatedUser = currentUser.copyWith(
        name: nameController.text.trim().isNotEmpty 
            ? nameController.text.trim() 
            : currentUser.name,
        address: addressController.text.trim(),
        country: countryController.value.text.trim(),
        mobileNumber: mobileNumberController.text.trim(),
        birthDate: birthDate,
      );

      // Save updated user data
      await _authStateController.updateUser(updatedUser);

      return true;
    } catch (e) {
      errorMessage.value = 'An unexpected error occurred';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Set birth date
  void setBirthDate(DateTime date) {
    birthDate = date;
    dateOfBirthController.value.text = date.toString().split(' ')[0];
  }

  /// Set country
  void setCountry(String country) {
    countryController.value.text = country;
  }

  /// Set mobile number
  void setMobileNumber(PhoneNumber number) {
    mobileNumber = number;
    mobileNumberController.text = number.phoneNumber ?? '';
  }

  /// Clear error message
  void clearError() {
    errorMessage.value = '';
  }

  /// Check if all required fields are filled
  bool get isFormValid {
    return nameController.text.trim().isNotEmpty &&
           addressController.text.trim().isNotEmpty &&
           countryController.value.text.trim().isNotEmpty &&
           mobileNumberController.text.trim().isNotEmpty &&
           birthDate != null;
  }

  /// Get welcome message based on social provider
  String get welcomeMessage {
    return 'Welcome! Please complete your profile to continue with ${socialProvider.value}.';
  }
}
