import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../controllers/base_screen_controller.dart';
import '../../../../helpers/device_util.dart';
import '../../domain/usecases/verify_email_usecase.dart';
import 'auth_state_controller.dart';

/// Clean Architecture Verify Email Controller using GetX
class VerifyEmailController extends BaseScreenController {
  final VerifyEmailUseCase _verifyEmailUseCase;
  final AuthStateController _authStateController;

  VerifyEmailController(
    this._verifyEmailUseCase,
    this._authStateController,
  );

  @override
  String get screenName => 'Verify Email';

  // UI state
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final codeController = TextEditingController();

  // Email and device name (passed as arguments)
  String? email;
  String? deviceName;

  @override
  Future<void> onInit() async {
    super.onInit();
    
    // Get arguments passed from previous screen
    final args = Get.arguments as Map<String, dynamic>?;
    email = args?['email'];
    deviceName = args?['deviceName'] ?? await DeviceUtil().getDeviceName();
  }

  @override
  void onClose() {
    codeController.dispose();
    super.onClose();
  }

  /// Verify email with code
  Future<bool> verifyEmail() async {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    if (email == null) {
      errorMessage.value = 'Email is required';
      return false;
    }

    isLoading.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    try {
      final result = await _verifyEmailUseCase.call(
        email: email!,
        code: codeController.text.trim(),
        deviceName: deviceName ?? 'Unknown Device',
      );

      if (result is Success) {
        // Update global auth state
        _authStateController.setUser(result.data.user);
        _authStateController.setToken(result.data.token);
        _authStateController.setIsLoggedIn(true);
        
        if (result.data.unreadNotificationCount != null) {
          _authStateController.setUnreadNotificationCount(
            result.data.unreadNotificationCount!,
          );
        }

        // Log analytics event
        await analytics.logSignUp(method: 'email');
        await analytics.setUserProperties(
          userId: result.data.user.id.toString(),
          country: result.data.user.country,
        );

        successMessage.value = 'Email verified successfully';
        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'An unexpected error occurred';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Clear messages
  void clearMessages() {
    errorMessage.value = '';
    successMessage.value = '';
  }

  /// Get display email (masked for privacy)
  String get displayEmail {
    if (email == null) return '';
    
    final parts = email!.split('@');
    if (parts.length != 2) return email!;
    
    final username = parts[0];
    final domain = parts[1];
    
    if (username.length <= 2) return email!;
    
    final maskedUsername = username[0] + 
        '*' * (username.length - 2) + 
        username[username.length - 1];
    
    return '$maskedUsername@$domain';
  }
}
